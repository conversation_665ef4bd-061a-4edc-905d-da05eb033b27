#ifndef UDP_TOSERVER_H
#define UDP_TOSERVER_H
void* Thead_ForwardUDP(void *lpPara);
void* Thread_RecDatafromDSS(void *lpPara);
void* Thread_ProcessCMSQue(void *lpPara);
void* Thread_CheckHeatBfromCMS(void *lpPara);
void* Thread_RecHeatBfromCMS(void *lpPara);
void* Thread_HeatBtoCMS(void *lpPara);
void *Thread_RecHeatBfromDBS(void *lpPara);
void *Thread_CheckHeatBfromDBS(void *lpPara);
void *Thread_HeatBtoDBS(void *lpPara);

void *Thread_GetGdzdhInfo(void *lpPara);
long what_time_is_it_now();
int what_time_is_it_sec();
void *Thead_LoadIDX(void *lpPara);
#endif // UDP_TOSERVER_H
