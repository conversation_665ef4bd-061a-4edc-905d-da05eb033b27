{"C_Cpp.errorSquiggles": "disabled", "files.associations": {"*.embeddedhtml": "html", "array": "cpp", "atomic": "cpp", "*.tcc": "cpp", "cctype": "cpp", "chrono": "cpp", "clocale": "cpp", "cmath": "cpp", "condition_variable": "cpp", "cstdarg": "cpp", "cstddef": "cpp", "cstdint": "cpp", "cstdio": "cpp", "cstdlib": "cpp", "cstring": "cpp", "ctime": "cpp", "cwchar": "cpp", "cwctype": "cpp", "deque": "cpp", "unordered_map": "cpp", "vector": "cpp", "exception": "cpp", "algorithm": "cpp", "map": "cpp", "memory": "cpp", "memory_resource": "cpp", "optional": "cpp", "ratio": "cpp", "string": "cpp", "string_view": "cpp", "system_error": "cpp", "tuple": "cpp", "type_traits": "cpp", "utility": "cpp", "fstream": "cpp", "initializer_list": "cpp", "iosfwd": "cpp", "iostream": "cpp", "istream": "cpp", "limits": "cpp", "mutex": "cpp", "new": "cpp", "ostream": "cpp", "sstream": "cpp", "stdexcept": "cpp", "streambuf": "cpp", "thread": "cpp", "cinttypes": "cpp", "typeinfo": "cpp", "__bit_reference": "cpp", "__split_buffer": "cpp", "locale": "cpp", "queue": "cpp", "__hash_table": "cpp", "__locale": "cpp", "__node_handle": "cpp", "__tree": "cpp", "__verbose_abort": "cpp", "bitset": "cpp", "charconv": "cpp", "complex": "cpp", "execution": "cpp", "forward_list": "cpp", "ios": "cpp", "print": "cpp", "stack": "cpp", "variant": "cpp"}}