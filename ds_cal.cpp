//地算计算模块
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <time.h>
#include <fcntl.h>
#include <unistd.h> 
#include <arpa/inet.h> 
#include <sys/socket.h>  
#include <netinet/in.h>
#include <map>

#include "math.h"
#include "tx2_filelog.h"

#include "DS_Data.h"
#include "udp_to_server.h"
#include "ds_station.h"
#include "crc.h"
#include "udp_to_server.h"
#include "gdzdh_cal.h"

#include <algorithm>

#define TESTStat  0
#define LOCO_DISTEN 2.5      // 机车到铁轨的距离值

using namespace std;

typedef struct _PCRDCARTESIAN{ //笛卡尔坐标系
	double x;
	double y;
	double z;
}PCRDCARTESIAN;

typedef struct _PCRDGEODETIC{
	double longitude; //经度
	double latitude;  //纬度 
	double height;    //大地高,可设为0
}PCRDGEODETIC;

struct SelLine{
  int lineid;
  float ds;
  int footindex;
};


mutex mtxLinePer_Que;
queue<float> LinePer_Que;

extern StaticVar G_V;

extern mutex lspThreadMux;
extern map<string, LSPThread> lsp_threads;//地算线程容器

map<int,string> mapTType;//车型容器
map<int, string>::iterator mapIter;  

char *makeword(char *line, char stop);

int LoadTrainType()//加载机车类型配置
{
 	FILE* fp;
    char fbuf[1024];
    sprintf(fbuf,"%scfg/TrainType.ini",G_V.cmd_path);
	fp = fopen(fbuf, "r");
    if(fp==NULL) return -1;
    while(1){
		char buf[512];
		if (feof(fp)) break;
		fgets(buf, 512, fp);
		if (strchr(buf, '=') != NULL) {
			char *idBuf;
			idBuf = makeword(buf, '=');
			if(buf[strlen(buf) - 1] =='\n')
				buf[strlen(buf) - 1] = '\0';
            mapTType.insert(map<int, string>::value_type (atoi(idBuf), buf));
			free(idBuf);
		}
    }
    fclose(fp);        
    return 0;
}

void GetTrainType(int ttid, char *TrainType)
{
   mapIter = mapTType.find(ttid);
   if(mapIter != mapTType.end()){  
      strcpy(TrainType,mapIter->second.data());
   }else{
      sprintf(TrainType,"WZ-%d",ttid); 
   }
}


float GetDS(DSPoint p1, DSPoint p2)
{
    return (float)sqrt((p1.x-p2.x)*(p1.x-p2.x)+(p1.y-p2.y)*(p1.y-p2.y));
}

void SetTrainDirect(int*train_line_direct, int value)
{
    for(int i=0;i<10;i++){
        train_line_direct[i]=value;
    }
}

void PutTrainDirDattoBuf(int*train_line_direct,int direct) {
    for (int i = 9; i > 0; i--) {
        train_line_direct[i] = train_line_direct[i - 1];
    }
    train_line_direct[0] = direct;
}

bool check_if_ok(int*train_line_direct) {

    if ((train_line_direct[0] == train_line_direct[1]))
        return true;
    else
        return false;
}


void SetLinePer(float* pre_LinePer,float per)
{
    for(int i=0;i<5;i++){
        pre_LinePer[i]=per;
    }
}

void PutLinePer(float* pre_LinePer,float per)
{
    for (int i = 4; i > 0; i--) {
        pre_LinePer[i] = pre_LinePer[i - 1];
    }
    pre_LinePer[0] = per;
}


void Check_Per_Direct(int*train_line_direct,float* pre_LinePer)
{
  if ((pre_LinePer[0] - pre_LinePer[1]) > 0.5 ) {
        PutTrainDirDattoBuf(train_line_direct,1);//面向终点
  }
  else if ((pre_LinePer[0] - pre_LinePer[1]) < -0.5 ) {
        PutTrainDirDattoBuf(train_line_direct,2);//面向起点
  }
}


void GeodeticToCartesian(PCRDCARTESIAN &pcc, PCRDGEODETIC &pcg)
{
	double B;        //纬度度数
	double L;        //经度度数
    double L0;       //中央经线度数
	double l;        //L-L0
	double t;        //tanB
	double m;        //ltanB
	double N;        //卯酉圈曲率半径 
	double q2;
	double x;        //高斯平面纵坐标
	double y;        //高斯平面横坐标
	double s;        //赤道至纬度B的经线弧长
	double f;        //参考椭球体扁率
	double e2;        //椭球第一偏心率
	double a;        //参考椭球体长半轴
	//double b;        //参考椭球体短半轴
	double a1;
	double a2;
	double a3;
	double a4;
	double b1;
	double b2;
	double b3;
	double b4;
	double c0;
	double c1;
	double c2;
	double c3;
	int Datum = 84;       //投影基准面类型：北京54基准面为54，西安80基准面为80，WGS84基准面为84
	int prjno = 0;        //投影带号
	int zonewide = 3;
	double IPI = 0.0174532925199433333333;        //3.1415926535898/180.0
	B = pcg.latitude; //纬度
	L = pcg.longitude; //经度
	if (zonewide == 6)
	{
		prjno = (int)(L / zonewide) + 1;
        L0 = prjno*zonewide - 3;
	}
	else
	{
		prjno = (int)((L - 1.5) / 3) + 1;
		L0 = prjno * 3;
	}

	if (Datum == 54)
	{
		a = 6378245;
		f = 1 / 298.3;
	}
	else if (Datum == 84)
	{
		a = 6378137;
		f = 1 / 298.257223563;
	}

	if (L > 112.51 && L < 112.57) //针对太原北站增加
		L0 = 111;                 //针对太原北站增加 
	
	L0 = L0*IPI;
	L = L*IPI;
	B = B*IPI;

	e2 = 2 * f - f*f;//(a*a-b*b)/(a*a);
	l = L - L0;
	t = tan(B);
	m = l * cos(B);
	N = a / sqrt(1 - e2* sin(B) * sin(B));
	q2 = e2 / (1 - e2)* cos(B)* cos(B);
	a1 = 1 + (double)3 / 4 * e2 + (double)45 / 64 * e2*e2 + (double)175 / 256 * e2*e2*e2 + (double)11025 / 16384 * e2*e2*e2*e2 + (double)43659 / 65536 * e2*e2*e2*e2*e2;
	a2 = (double)3 / 4 * e2 + (double)15 / 16 * e2*e2 + (double)525 / 512 * e2*e2*e2 + (double)2205 / 2048 * e2*e2*e2*e2 + (double)72765 / 65536 * e2*e2*e2*e2*e2;
	a3 = (double)15 / 64 * e2*e2 + (double)105 / 256 * e2*e2*e2 + (double)2205 / 4096 * e2*e2*e2*e2 + (double)10359 / 16384 * e2*e2*e2*e2*e2;
	a4 = (double)35 / 512 * e2*e2*e2 + (double)315 / 2048 * e2*e2*e2*e2 + (double)31185 / 13072 * e2*e2*e2*e2*e2;
	b1 = a1*a*(1 - e2);
	b2 = (double)-1 / 2 * a2*a*(1 - e2);
	b3 = (double)1 / 4 * a3*a*(1 - e2);
	b4 = (double)-1 / 6 * a4*a*(1 - e2);
	c0 = b1;
	c1 = 2 * b2 + 4 * b3 + 6 * b4;
	c2 = -(8 * b3 + 32 * b4);
	c3 = 32 * b4;
	s = c0*B + cos(B)*(c1*sin(B) + c2*sin(B)*sin(B)*sin(B) + c3*sin(B)*sin(B)*sin(B)*sin(B)*sin(B));
	x = s + (double)1 / 2 * N*t*m*m + (double)1 / 24 * (5 - t*t + 9 * q2 + 4 * q2*q2)*N*t*m*m*m*m + (double)1 / 720 * (61 - 58 * t*t + t*t*t*t)*N*t*m*m*m*m*m*m;
	y = N*m + (double)1 / 6 * (1 - t*t + q2)*N*m*m*m + (double)1 / 120 * (5 - 18 * t*t + t*t*t*t - 14 * q2 - 58 * q2*t*t)*N*m*m*m*m*m;

	//y = y + 1000000 * prjno + 500000;
	//pcc.x = x;
	//pcc.y = y - 38000000;
	//pcc.z = pcg.height;

	y = y  + 500000;
	pcc.x = x;
	pcc.y = y;
	pcc.z = pcg.height;

}

bool LoadStation_Map(int stationID, int TLJ,DStation* st_map)
{
#if TESTStat
  char *filename="/home/<USER>/temp/xzb.bds";
#else
  char filename[512];
  sprintf(filename,"%s/%d/%d/%d.bds",G_V.Map_Path,TLJ,stationID,stationID);
#endif
  FILE* fp;
  long long cur_index = 0;
  fp = fopen(filename, "rb");
  fseek(fp, 0, SEEK_END);//移到文件尾
  long long fileSize = ftell(fp);
  fseek(fp, 0, SEEK_SET);//移到文件头
  byte* fbuf = (byte*)malloc(fileSize);//文件字节数
  fread(fbuf, 1, fileSize, fp);
  uint32_t crc = CRC32(fbuf, fileSize-6);
  uint32_t* oldcrc;
  oldcrc = (uint32_t*) (&fbuf[fileSize - 4]);
  byte ch1 = fbuf[fileSize - 5];
  byte ch2 = fbuf[fileSize - 6];
  if ( ch1 !=0xFE || ch2!= 0xFE || crc != *oldcrc) {
      return false;
  }
  fclose(fp);

  USHORT map_v;
  map_v = *(USHORT*)&fbuf[0];

  st_map->areaID = fbuf[2];
  for (int i = 0; i < 20; i++) {
      st_map->areaName[i] = fbuf[3 + i];
  }
  st_map->stationID = *(USHORT*)&fbuf[23];
  for (int i = 0; i < 30; i++) {
      st_map->stationName[i] = fbuf[25 + i];
  }
  st_map->stationType = fbuf[55];
  st_map->rectArea.xmin = *(float*)&fbuf[56];
  st_map->rectArea.xmax = *(float*)&fbuf[60];
  st_map->rectArea.ymin = *(float*)&fbuf[64];
  st_map->rectArea.ymax = *(float*)&fbuf[68];
  st_map->pyPoints_N = *(USHORT*)&fbuf[72];
  cur_index = 74;
  DPPoint tempDSPt;
  for (int i = 0; i < st_map->pyPoints_N; i++) {
     tempDSPt.x = *(int*)&fbuf[cur_index];
     cur_index = cur_index + 4;
     tempDSPt.y = *(int*)&fbuf[cur_index];
     cur_index = cur_index + 4;
     st_map->pyPoints.push_back(tempDSPt);
  }

  while (1) {
      byte c = fbuf[cur_index];
      if ( c != 0xfe) {
          if (cur_index > fileSize - 2) break;
          cur_index = cur_index + 1;
          continue;
      }
      cur_index = cur_index + 2;
      switch (fbuf[cur_index - 1]) {
          case 0://道岔
          {
              USHORT obj_n;
              obj_n = *(USHORT*)&fbuf[cur_index];
              st_map->Turnout_N = obj_n;
              cur_index = cur_index + 2;
              for (int ii = 0; ii < obj_n;ii++) {
                  DSTurnout temp_tnot;
                  temp_tnot.t_id = *(USHORT*)&fbuf[cur_index];
                  cur_index = cur_index + 2;
                  for (int j = 0; j < 12; j++) {
                      temp_tnot.to_name[j] = fbuf[cur_index + j];
                  }
                  cur_index = cur_index + 12;
                  temp_tnot.position.x = *(float*)&fbuf[cur_index];
                  cur_index = cur_index + 4;
                  temp_tnot.position.y = *(float*)&fbuf[cur_index];
                  cur_index = cur_index + 4;
                  st_map->Turnouts.push_back(temp_tnot);
              }
              break;
          }
          case 1://信号灯
          {
              USHORT obj_n;
              obj_n = *(USHORT*)&fbuf[cur_index];
              st_map->Lamp_N = obj_n;
              cur_index = cur_index + 2;
              for (int ii = 0; ii < obj_n; ii++) {
                  DSLamp t_lamp;
                  t_lamp.lamp_index = *(USHORT*)&fbuf[cur_index];
                  cur_index = cur_index + 2;
                  t_lamp.lamptype = *(byte*)&fbuf[cur_index];
                  cur_index = cur_index + 1;
                  for (int j = 0; j < 12; j++) {
                      t_lamp.lamp_name[j] = fbuf[cur_index + j];
                  }
                  cur_index = cur_index + 12;
                  t_lamp.lampkind = *(byte*)&fbuf[cur_index];
                  cur_index = cur_index + 1;
                  t_lamp.lampleftRight = *(byte*)&fbuf[cur_index];
                  cur_index = cur_index + 1;
                  t_lamp.lampheight = *(byte*)&fbuf[cur_index];
                  cur_index = cur_index + 1;
                  t_lamp.actualPos.x = *(float*)&fbuf[cur_index];
                  cur_index = cur_index + 4;
                  t_lamp.actualPos.y = *(float*)&fbuf[cur_index];
                  cur_index = cur_index + 4;
                  t_lamp.position.x = *(float*)&fbuf[cur_index];
                  cur_index = cur_index + 4;
                  t_lamp.position.y = *(float*)&fbuf[cur_index];
                  cur_index = cur_index + 4;
                  t_lamp.linepos = *(float*)&fbuf[cur_index];
                  cur_index = cur_index + 4;
                  st_map->Lamps.push_back(t_lamp);
              }
              break;
          }
          case 2: //接触网终点标
          {
              USHORT obj_n = 0;
              obj_n = *(USHORT*)&fbuf[cur_index];
              st_map->PowerEnd_N = obj_n;
              cur_index = cur_index + 2;
              for (int ii = 0; ii < obj_n; ii++) {
                  DSPowerEnd t_PoweED;
                  t_PoweED.PowerEnd_index= *(USHORT*)&fbuf[cur_index];
                  cur_index = cur_index + 2;
                  t_PoweED.PowerEnd_type = *(byte*)&fbuf[cur_index];
                  cur_index = cur_index + 1;
                  t_PoweED.position.x = *(float*)&fbuf[cur_index];
                  cur_index = cur_index + 4;
                  t_PoweED.position.y = *(float*)&fbuf[cur_index];
                  cur_index = cur_index + 4;
                  t_PoweED.linepos = *(float*)&fbuf[cur_index];
                  cur_index = cur_index + 4;
                  st_map->Powerends.push_back(t_PoweED);
              }
              break;
          }
          case 3: //脱轨器
          {
              USHORT obj_n = 0;
              obj_n = *(USHORT*)&fbuf[cur_index];
              st_map->Derailer_N = obj_n;
              cur_index = cur_index + 2;
              for (int ii = 0; ii < obj_n; ii++) {
                  DSDerailer t_DSDe;
                  t_DSDe.Derailer_index = *(USHORT*)&fbuf[cur_index];
                  cur_index = cur_index + 2;
                  t_DSDe.Derailer_type = *(byte*)&fbuf[cur_index];
                  cur_index = cur_index + 1;
                  t_DSDe.position.x = *(float*)&fbuf[cur_index];
                  cur_index = cur_index + 4;
                  t_DSDe.position.y = *(float*)&fbuf[cur_index];
                  cur_index = cur_index + 4;
                  t_DSDe.linepos = *(float*)&fbuf[cur_index];
                  cur_index = cur_index + 4;
                  st_map->Derailers.push_back(t_DSDe);
              }
              break;
          }
          case 4: //土挡
          {
              USHORT obj_n = 0;
              obj_n = *(USHORT*)&fbuf[cur_index];
              st_map->Stoppoint_N = obj_n;
              cur_index = cur_index + 2;
              for (int ii = 0; ii < obj_n; ii++) {
                  DStoppoint t_stop;
                  t_stop.s_id = *(USHORT*)&fbuf[cur_index];
                  cur_index = cur_index + 2;
                  t_stop.Stype = *(byte*)&fbuf[cur_index];
                  cur_index = cur_index + 1;
                  for (int j = 0; j < 12; j++) {
                      t_stop.Stoppoint_name[j] = fbuf[cur_index + j];
                  }
                  cur_index = cur_index + 12;
                  t_stop.position.x = *(float*)&fbuf[cur_index];
                  cur_index = cur_index + 4;
                  t_stop.position.y = *(float*)&fbuf[cur_index];
                  cur_index = cur_index + 4;
                  t_stop.line_pos = *(float*)&fbuf[cur_index];
                  cur_index = cur_index + 4;
                  st_map->Stoppoints.push_back(t_stop);
              }
              break;
          }
          case 5: //站界
          {
              USHORT obj_n = 0;
              obj_n = *(USHORT*)&fbuf[cur_index];
              st_map->StLimit_N = obj_n;
              cur_index = cur_index + 2;
              for (int ii = 0; ii < obj_n; ii++) {
                  DStationLimit t_stlim;
                  t_stlim.StLimit_index = *(USHORT*)&fbuf[cur_index];
                  cur_index = cur_index + 2;
                  t_stlim.StLimit_type = *(byte*)&fbuf[cur_index];
                  cur_index = cur_index + 1;
                  t_stlim.position.x = *(float*)&fbuf[cur_index];
                  cur_index = cur_index + 4;
                  t_stlim.position.y = *(float*)&fbuf[cur_index];
                  cur_index = cur_index + 4;
                  t_stlim.linepos = *(float*)&fbuf[cur_index];
                  cur_index = cur_index + 4;
                  st_map->StLimits.push_back(t_stlim);
              }
              break;
          }
          case 6: //禁停区
          {
              USHORT obj_n = 0;
              obj_n = *(USHORT*)&fbuf[cur_index];
              st_map->NPA_N = obj_n;
              cur_index = cur_index + 2;
              for (int ii = 0; ii < obj_n; ii++) {
                  DSNoParkingArea t_npa;
                  t_npa.NPA_index = *(USHORT*)&fbuf[cur_index];
                  cur_index = cur_index + 2;

                  t_npa.begin_pos.x = *(float*)&fbuf[cur_index];
                  cur_index = cur_index + 4;
                  t_npa.begin_pos.y = *(float*)&fbuf[cur_index];
                  cur_index = cur_index + 4;
                  t_npa.begin_linepos = *(float*)&fbuf[cur_index];
                  cur_index = cur_index + 4;

                  t_npa.end_pos.x = *(float*)&fbuf[cur_index];
                  cur_index = cur_index + 4;
                  t_npa.end_pos.y = *(float*)&fbuf[cur_index];
                  cur_index = cur_index + 4;
                  t_npa.end_linepos = *(float*)&fbuf[cur_index];
                  cur_index = cur_index + 4;

                  st_map->NPAs.push_back(t_npa);
              }
              break;
          }
          case 7: //特殊防控项点
          {
              USHORT obj_n = 0;
              obj_n = *(USHORT*)&fbuf[cur_index];
              st_map->SpecPoint_N = obj_n;
              cur_index = cur_index + 2;
              for (int ii = 0; ii < obj_n; ii++) {
                  DSpecialPoint t_sp;
                  t_sp.sp_index = *(USHORT*)&fbuf[cur_index];
                  cur_index = cur_index + 2;
                  t_sp.sp_type = *(byte*)&fbuf[cur_index];
                  cur_index = cur_index + 1;
                  for (int j = 0; j < 12; j++) {
                      t_sp.SPName[j] = fbuf[cur_index + j];
                  }
                  cur_index = cur_index + 12;
                  for (int j = 0; j < 50; j++) {
                      t_sp.speechtxt[j] = fbuf[cur_index + j];
                  }
                  cur_index = cur_index + 50;
                  t_sp.position.x = *(float*)&fbuf[cur_index];
                  cur_index = cur_index + 4;
                  t_sp.position.y = *(float*)&fbuf[cur_index];
                  cur_index = cur_index + 4;
                  t_sp.linepos = *(float*)&fbuf[cur_index];
                  cur_index = cur_index + 4;
                  st_map->SpecPoints.push_back(t_sp);
              }
              break;
          }
          case 8: //线路
          {
              USHORT obj_n = 0;
              obj_n = *(USHORT*)&fbuf[cur_index];
              st_map->Rail_N = obj_n;
              cur_index = cur_index + 2;
              for (int ii = 0; ii < obj_n; ii++) {
                  DSPline t_rail;
                  t_rail.PlineID = *(USHORT*)&fbuf[cur_index];
                  cur_index = cur_index + 2;
                  for (int j = 0; j < 20; j++) {
                      t_rail.line_name[j] = fbuf[cur_index + j];
                  }
                  cur_index = cur_index + 20;
                  t_rail.up_type = *(byte*)&fbuf[cur_index];
                  cur_index = cur_index + 1;
                  t_rail.NonPowerStat = *(byte*)&fbuf[cur_index];
                  cur_index = cur_index + 1;
                  t_rail.LineLength = *(float*)&fbuf[cur_index];
                  cur_index = cur_index + 4;
                  t_rail.preLineN = *(byte*)&fbuf[cur_index];
                  cur_index = cur_index + 1;
                  for (int nn = 0; nn < t_rail.preLineN; nn++) {
                      USHORT t_preid;
                      t_preid = *(USHORT*)&fbuf[cur_index];
                      t_rail.preLineIDs.push_back(t_preid);
                      cur_index = cur_index + 2;
                  }
                  t_rail.nextLineN = *(byte*)&fbuf[cur_index];
                  cur_index = cur_index + 1;
                  for (int nn = 0; nn < t_rail.nextLineN; nn++) {
                      USHORT t_nextid;
                      t_nextid = *(USHORT*)&fbuf[cur_index];
                      t_rail.nextLineIDs.push_back(t_nextid);
                      cur_index = cur_index + 2;
                  }
                  t_rail.totalPointsN = *(USHORT*)&fbuf[cur_index];
                  cur_index = cur_index + 2;
                  for (int nn = 0; nn < t_rail.totalPointsN; nn++) {
                      DSPoint t_point;
                      t_point.x= *(float*)&fbuf[cur_index];
                      cur_index = cur_index + 4;
                      t_point.y = *(float*)&fbuf[cur_index];
                      cur_index = cur_index + 4;
                      t_rail.Points.push_back(t_point);
                  }
                  t_rail.totalLampN = *(byte*)&fbuf[cur_index];
                  cur_index = cur_index + 1;
                  for (int nn = 0; nn < t_rail.totalLampN; nn++) {
                      USHORT t_lampid;
                      t_lampid = *(USHORT*)&fbuf[cur_index];
                      cur_index = cur_index + 2;
                      t_rail.Lamps.push_back(t_lampid);
                  }
                  t_rail.totalTurnoutN = *(byte*)&fbuf[cur_index];
                  cur_index = cur_index + 1;
                  for (int nn = 0; nn < t_rail.totalTurnoutN; nn++) {
                      USHORT t_toid;
                      t_toid = *(USHORT*)&fbuf[cur_index];
                      cur_index = cur_index + 2;
                      t_rail.Turnouts.push_back(t_toid);
                  }
                  t_rail.totalPowerEndN = *(byte*)&fbuf[cur_index];
                  cur_index = cur_index + 1;
                  for (int nn = 0; nn < t_rail.totalPowerEndN; nn++) {
                      USHORT t_toid;
                      t_toid = *(USHORT*)&fbuf[cur_index];
                      cur_index = cur_index + 2;
                      t_rail.Powerends.push_back(t_toid);
                  }
                  t_rail.totalStoppointN = *(byte*)&fbuf[cur_index];
                  cur_index = cur_index + 1;
                  for (int nn = 0; nn < t_rail.totalStoppointN; nn++) {
                      USHORT t_toid;
                      t_toid = *(USHORT*)&fbuf[cur_index];
                      cur_index = cur_index + 2;
                      t_rail.Stoppoints.push_back(t_toid);
                  }
                  t_rail.totalDerailerN = *(byte*)&fbuf[cur_index];
                  cur_index = cur_index + 1;
                  for (int nn = 0; nn < t_rail.totalDerailerN; nn++) {
                      USHORT t_toid;
                      t_toid = *(USHORT*)&fbuf[cur_index];
                      cur_index = cur_index + 2;
                      t_rail.Derailers.push_back(t_toid);
                  }
                  t_rail.totalStLimitN = *(byte*)&fbuf[cur_index];
                  cur_index = cur_index + 1;
                  for (int nn = 0; nn < t_rail.totalStLimitN; nn++) {
                      USHORT t_toid;
                      t_toid = *(USHORT*)&fbuf[cur_index];
                      cur_index = cur_index + 2;
                      t_rail.StLimits.push_back(t_toid);
                  }
                  t_rail.totalNPAN = *(byte*)&fbuf[cur_index];
                  cur_index = cur_index + 1;
                  for (int nn = 0; nn < t_rail.totalNPAN; nn++) {
                      USHORT t_toid;
                      t_toid = *(USHORT*)&fbuf[cur_index];
                      cur_index = cur_index + 2;
                      t_rail.NPAs.push_back(t_toid);
                  }
                  t_rail.totalSPN = *(byte*)&fbuf[cur_index];
                  cur_index = cur_index + 1;
                  for (int nn = 0; nn < t_rail.totalSPN; nn++) {
                      USHORT t_toid;
                      t_toid = *(USHORT*)&fbuf[cur_index];
                      cur_index = cur_index + 2;
                      t_rail.SpecPoints.push_back(t_toid);
                  }
                  st_map->Railways.push_back(t_rail);
                  st_map->Railways_initial.push_back(t_rail);
              }
              break;
          }
          case 0xFE: //校验
          {
              free(fbuf);
              return true;
          }
          default:
              break;
      }
  }

  free(fbuf);
  return true;
}

int isbetween_d(float a, float b, float c)//双精度型 is c between a and b ?
{
    int stat = 0;
    if (a<=b && c >= a && c <= b)
        stat = 1;
    else if (a>=b && c >= b && c <= a)
        stat = 1;

    return stat;
}

//点(x,y)距(x1,y1)和(x2,y2)两点连线的距离
//直线方程ax+bx+c=0;
float find_d_PointtoLine(float x1, float y1, float x2, float y2, float x, float y,
                          float *tx_adjuest_lo, float *ty_adjuest_lo)
{
    float k, a, b, c, d;
    if (x2 != x1) {
        k = (y2 - y1) / (x2 - x1);
        a = k; b = -1; c = y1 - k * x1;
        d = abs((a * x + b * y + c) / sqrt(a * a + b * b));
        //if (d < LOCO_DISTEN ) {//求机车的调整位置，垂足点
            *tx_adjuest_lo = (b * b * x - a * b * y - a * c) / (a * a + b * b);
            *ty_adjuest_lo = (-a * b * x + a * a * y - b * c) / (a * a + b * b);
        //}
    }
    else {
        d = abs(x - x1);
        *tx_adjuest_lo = x1;
        *ty_adjuest_lo = y;
    }

    return d;
}

//计算点到多线段的距离; -1表示点不在多线段上
float find_d_toMulPline(DStation *curStation,int PlineID, float x, float y, int *footindex,
                            float *tx_adjuest_dl, float *ty_adjuest_dl)
{
    float x1, y1, x2, y2;
    float d = -1;
    float d_min = 100000;
    int i_min = 0;
    float adx,ady;
    for (int i = 1; i < curStation->Railways[PlineID].totalPointsN; i++)
    {
        x1 = curStation->Railways[PlineID].Points[i - 1].x;
        y1 = curStation->Railways[PlineID].Points[i - 1].y;
        x2 = curStation->Railways[PlineID].Points[i].x;
        y2 = curStation->Railways[PlineID].Points[i].y;

        if (abs(x1-x2)>=abs(y1-y2)) {
            if (isbetween_d(x1, x2, x)) {
                d = find_d_PointtoLine(x1, y1, x2, y2, x, y, &adx, &ady);
                if (d < d_min) {
                    d_min = d;
                    i_min = i;
                    *tx_adjuest_dl = adx;
                    *ty_adjuest_dl = ady;
                }
            }
        }
        else {
            if (isbetween_d(y1, y2, y)) {
                d = find_d_PointtoLine(x1, y1, x2, y2, x, y, &adx, &ady);
                if (d < d_min) {
                    d_min = d;
                    i_min = i;
                    *tx_adjuest_dl = adx;
                    *ty_adjuest_dl = ady;
                }
            }
        }
    }
    if (i_min > 0) {
        *footindex = i_min-1;
    }
    if (d_min < 10000) d = d_min;
    return d;
}

//获取离机车最近的铁道，返回轨道编号
int GetTheClosestLineToTheTrain(DStation *curStation,DSPoint curTrainPos,float* min_ds_1, int* cur_foot_index,
                                 float *tx_adjuest_dl, float *ty_adjuest_dl,SelLine closeLine[])
{
    float ds;
    float min_ds = 10000;
    int min_i = -1;
    float adx,ady;


    for(int i=0;i<3;i++){
        closeLine[i].ds=10000;
        closeLine[i].footindex=-1;
        closeLine[i].lineid =-1;
    }

    try
    {
        for (int i = 0; i < curStation->Rail_N; i++)
        {
            int tempfoot;

            ds = find_d_toMulPline(curStation, curStation->Railways[i].PlineID,curTrainPos.x, curTrainPos.y,
                                                    &tempfoot, &adx, &ady);

            if (ds >= 0 && ds <= min_ds) {
                closeLine[2] = closeLine[1];
                closeLine[1] = closeLine[0];
                closeLine[0].lineid=curStation->Railways[i].PlineID;
                closeLine[0].ds =ds;
                closeLine[0].footindex = tempfoot;
                min_ds = ds;
                min_i = curStation->Railways[i].PlineID;

                *tx_adjuest_dl = adx;
                *ty_adjuest_dl = ady;
                *min_ds_1 = min_ds;
                *cur_foot_index=tempfoot;
            }
            else if (ds <closeLine[1].ds && ds > closeLine[0].ds) {
                closeLine[2] = closeLine[1];
                closeLine[1].ds = ds;
                closeLine[1].lineid = curStation->Railways[i].PlineID;
                closeLine[1].footindex = tempfoot;
            }
            else if (ds <closeLine[2].ds && ds > closeLine[1].ds) {
                closeLine[2].ds = ds;
                closeLine[2].lineid = curStation->Railways[i].PlineID;
                closeLine[2].footindex = tempfoot;
            }
        }
    }
    catch (...) {
        return -1;
    }
    return min_i;
}

//检查股道的切换是否合理
//-1__不合理，跳出   1__保持现有股道不变   2__切换股道
int  CheckTheLineIDChange(DStation *curStation, DSPoint curTrainPos,int lineid, int trainonlinedirect, int* cur_foot_index, float *tx_foot, float *ty_foot,
                            int *line_change_waittime_ifneeded,SelLine closeLine[])
{
    bool IDchange = false;
    int stat=1;
    int tempfoot=-1;
    float adx,ady;
    //如果当前股道是距离机车第二近的股道，且误差在范围内，股道不需要切换
    if (closeLine[1].lineid == lineid) {
        float zz = find_d_toMulPline(curStation, lineid, curTrainPos.x, curTrainPos.y, &tempfoot,&adx,&ady);
        if (zz >= 0 && zz < LOCO_DISTEN) {
            *tx_foot = adx;
            *ty_foot = ady;
            *cur_foot_index = tempfoot;
            return -2;
        }
    }
    if (trainonlinedirect == 1 ) {//机车方向和股道方向一致
        float zz;
        switch(curStation->Railways[lineid].nextLineN){
           case 1://当前线路前方有一条线路路
                int tempfoot;
                zz = find_d_toMulPline(curStation, curStation->Railways[lineid].nextLineIDs[0],
                                       curTrainPos.x, curTrainPos.y, &tempfoot,&adx,&ady);
                if (zz < 0) { //计算机车距离该线路的距离，不合理，跳出回调重新计算
                    *line_change_waittime_ifneeded = *line_change_waittime_ifneeded + 1;
                    if (*line_change_waittime_ifneeded > 2) {//等待30包数据后还是不合理，跳出回调函数，重新定位
                            *line_change_waittime_ifneeded = 0;
                            return -1;
                     }
                     else {//等待
                            float zz = find_d_toMulPline(curStation,lineid, curTrainPos.x, curTrainPos.y, &tempfoot,&adx,&ady);
                            if (zz >= 0) {
                                *tx_foot = adx;
                                *ty_foot = ady;
                                *cur_foot_index = tempfoot;
                                return -2;
                            }
                            else {
                                *line_change_waittime_ifneeded = 0;
                                return -1;
                            }
                     }
                }
                *tx_foot = adx;
                *ty_foot = ady;
                *cur_foot_index = tempfoot;
                *line_change_waittime_ifneeded = 0;
                IDchange = true;
                stat = 1;
                break;
           case 2:
                if(closeLine[1].ds < LOCO_DISTEN){//如果倒数第二近股道距离机车距离也小于阈值，继续等待
                    return -2;
                }else{
                    IDchange = true;
                    stat = 1;            
                }
                /*for (int ii = 0; ii < curStation->Railways[lineid].nextLineN; ii++)//找出距离最短的线路
                {
                    int tempid;
                    tempid = curStation->Railways[lineid].nextLineIDs[ii];
                        if (tempid == closeLine[0].lineid) {
                        IDchange = true;
                        stat = 1;
                        break;
                    }
                }
                if (!IDchange) {
                    *line_change_waittime_ifneeded = *line_change_waittime_ifneeded + 1;
                    if (*line_change_waittime_ifneeded > 30) {//等待13包数据后还是不合理，跳出回调函数，重新定位
                        *line_change_waittime_ifneeded = 0;
                        return -1;
                    }
                    else {
                        return -2;
                    }
                }*/
                break;

           default:
                *line_change_waittime_ifneeded = 0;
                return -1;
                break;
        }
   }
   else if (trainonlinedirect == 2) { //机车方向和股道方向相反
        float zz;
        switch(curStation->Railways[lineid].preLineN){
          case 1://当前线路前方有一条线路路
                int tempfoot;
                zz = find_d_toMulPline(curStation, curStation->Railways[lineid].preLineIDs[0],
                                       curTrainPos.x, curTrainPos.y, &tempfoot,&adx,&ady);
                if (zz < 0) { //计算机车距离该线路的距离，不合理，跳出回调重新计算
                    *line_change_waittime_ifneeded = *line_change_waittime_ifneeded + 1;
                    if (*line_change_waittime_ifneeded > 2) {//等待30包数据后还是不合理，跳出回调函数，重新定位
                            *line_change_waittime_ifneeded = 0;
                            return -1;
                     }
                     else {//等待
                            float zz = find_d_toMulPline(curStation,lineid, curTrainPos.x, curTrainPos.y, &tempfoot,&adx,&ady);
                            if (zz >= 0) {
                                *tx_foot = adx;
                                *ty_foot = ady;
                                *cur_foot_index = tempfoot;
                                return -2;
                            }
                            else {
                                *line_change_waittime_ifneeded = 0;
                                return -1;
                            }
                     }
                }
                *tx_foot = adx;
                *ty_foot = ady;
                *cur_foot_index = tempfoot;
                *line_change_waittime_ifneeded = 0;
                IDchange = true;
                stat = 1;
                break;

          case 2://当前线路前方有两条线路

                if(closeLine[1].ds < LOCO_DISTEN){//如果倒数第二近股道距离机车距离也小于阈值，继续等待
                    return -2;
                }else{
                    IDchange = true;
                    stat = 1;            
                }
                /*for (int ii = 0; ii < curStation->Railways[lineid].preLineN; ii++)//找出距离最短的线路
                {
                    int tempid;
                    tempid = curStation->Railways[lineid].preLineIDs[ii];
                    if (tempid == closeLine[0].lineid) {
                        IDchange = true;
                        stat = 1;
                        break;
                    }
                }
                if (!IDchange) {
                    *line_change_waittime_ifneeded = *line_change_waittime_ifneeded + 1;
                    if (*line_change_waittime_ifneeded > 30) {//等待13包数据后还是不合理，跳出回调函数，重新定位
                        *line_change_waittime_ifneeded = 0;
                        return -1;
                    }
                    else {
                        return -2;
                    }
                }*/
                break;

          default:
                *line_change_waittime_ifneeded = 0;
                return -1;
                break;
        }

   }

    if (!IDchange) {
        return -3;
    }
    else {
        return stat;
    }
}

//根据前后股道的顺序确定机车在切换股道上的初始方向
int GetTrainDirectInitial(DStation *curStation,int preLID, int LID)
{

    float x1, y1, x2, y2;
    float x3, y3, x4, y4;
    float dx, dy;
    float d1, d2;
    if (preLID < 0) preLID = LID;

    x1 = curStation->Railways[preLID].Points[0].x;
    y1 = curStation->Railways[preLID].Points[0].y;
    x2 = curStation->Railways[preLID].Points[curStation->Railways[preLID].totalPointsN - 1].x;
    y2 = curStation->Railways[preLID].Points[curStation->Railways[preLID].totalPointsN - 1].y;

    x3 = curStation->Railways[LID].Points[0].x;
    y3 = curStation->Railways[LID].Points[0].y;
    dx = x3 - x1;
    dy = y3 - y1;
    d1 = sqrt(dx * dx + dy * dy);
    dx = x3 - x2;
    dy = y3 - y2;
    d2 = sqrt(dx * dx + dy * dy);

    if (d2 < 0.5 || d1 < 0.5)
        return 1;

    x4 = curStation->Railways[LID].Points[curStation->Railways[LID].totalPointsN - 1].x;
    y4 = curStation->Railways[LID].Points[curStation->Railways[LID].totalPointsN - 1].y;

    dx = x4 - x1;
    dy = y4 - y1;
    d1 = sqrt(dx * dx + dy * dy);
    dx = x4 - x2;
    dy = y4 - y2;
    d2 = sqrt(dx * dx + dy * dy);

    if (d2 < 0.5 || d1 < 0.5)
        return 2;

}

//确定当前线路机车前方的信号灯
void FindLamponLine(DStation *stMap, float trainPer, int PlineID, int trainonlinedirect,vector<FKPoint> *KeyPointS)
{
    for(int i =0; i<stMap->Railways[PlineID].totalLampN; i++){//1 信号灯
        USHORT ti =  stMap->Railways[PlineID].Lamps[i];
        if((trainonlinedirect==1) && (stMap->Lamps[ti].lamptype == trainonlinedirect)
                                  && (trainPer<stMap->Lamps[ti].linepos) ){
                   FKPoint tempPoint;
                   tempPoint.PointType=1;
                   tempPoint.PointID = stMap->Lamps[ti].lamp_index;
                   strcpy(tempPoint.PointName,stMap->Lamps[ti].lamp_name);
                   tempPoint.PointV = stMap->Lamps[ti].lampvalue;
                   tempPoint.PointDis=(stMap->Lamps[ti].linepos-trainPer)*100;
                   KeyPointS->push_back(tempPoint);

        }else if((trainonlinedirect==2) && (stMap->Lamps[ti].lamptype == trainonlinedirect)
                                       && (trainPer>stMap->Lamps[ti].linepos)){
            FKPoint tempPoint;
            tempPoint.PointType=1;
            tempPoint.PointID = stMap->Lamps[ti].lamp_index;
            strcpy(tempPoint.PointName,stMap->Lamps[ti].lamp_name);
            tempPoint.PointV = stMap->Lamps[ti].lampvalue;
            tempPoint.PointDis=(trainPer-stMap->Lamps[ti].linepos)*100;
            KeyPointS->push_back(tempPoint);
        }
    }

}
//查前方进路上的信号灯
void FindLamponRail(DStation *stMap, float tempdis, int PlineID, int trainonlinedirect,vector<FKPoint> *KeyPointS)
{
    for(int i =0; i<stMap->Railways[PlineID].totalLampN; i++){//1 信号灯
        USHORT ti =  stMap->Railways[PlineID].Lamps[i];
        if((trainonlinedirect==1) && (stMap->Lamps[ti].lamptype == trainonlinedirect)){
                   FKPoint tempPoint;
                   tempPoint.PointType=1;
                   tempPoint.PointID = stMap->Lamps[ti].lamp_index;
                   strcpy(tempPoint.PointName,stMap->Lamps[ti].lamp_name);
                   tempPoint.PointV = stMap->Lamps[ti].lampvalue;
                   tempPoint.PointDis=(tempdis+stMap->Lamps[ti].linepos)*100;
                   KeyPointS->push_back(tempPoint);

        }else if((trainonlinedirect==2) && (stMap->Lamps[ti].lamptype == trainonlinedirect)){
            FKPoint tempPoint;
            tempPoint.PointType=1;
            tempPoint.PointID = stMap->Lamps[ti].lamp_index;
            strcpy(tempPoint.PointName,stMap->Lamps[ti].lamp_name);
            tempPoint.PointV = stMap->Lamps[ti].lampvalue;
            tempPoint.PointDis=(tempdis+stMap->Railways[PlineID].LineLength-stMap->Lamps[ti].linepos)*100;
            KeyPointS->push_back(tempPoint);
        }
    }

}

//确定当前线路机车前方的接触网终点标
void FindPwndonLine(DStation *stMap, float trainPer, int PlineID, int trainonlinedirect,vector<FKPoint> *KeyPointS)
{
    for(int i =0; i<stMap->Railways[PlineID].totalPowerEndN; i++){ //2 接触网终点标
        USHORT ti =  stMap->Railways[PlineID].Powerends[i];
        if((trainonlinedirect==1) && (stMap->Powerends[ti].PowerEnd_type == trainonlinedirect)
                                  && (trainPer<stMap->Powerends[ti].linepos) ){
                   FKPoint tempPoint;
                   tempPoint.PointType=2;
                   tempPoint.PointID = stMap->Powerends[ti].PowerEnd_index;
                   strcpy(tempPoint.PointName,"终点标");
                   tempPoint.PointV = 0;
                   tempPoint.PointDis=(stMap->Powerends[ti].linepos-trainPer)*100;
                   KeyPointS->push_back(tempPoint);

        }else if((trainonlinedirect==2) && (stMap->Powerends[ti].PowerEnd_type == trainonlinedirect)
                                       && (trainPer>stMap->Powerends[ti].linepos)){
            FKPoint tempPoint;
            tempPoint.PointType=2;
            tempPoint.PointID = stMap->Powerends[ti].PowerEnd_index;
            strcpy(tempPoint.PointName,"终点标");
            tempPoint.PointV = 0;
            tempPoint.PointDis=(trainPer-stMap->Powerends[ti].linepos)*100;
            KeyPointS->push_back(tempPoint);
        }
    }
}
//查前方进路上的接触网终点标
void FindPwndonRail(DStation *stMap, float tempdis, int PlineID, int trainonlinedirect,vector<FKPoint> *KeyPointS)
{
    for(int i =0; i<stMap->Railways[PlineID].totalPowerEndN; i++){ //2 接触网终点标
        USHORT ti =  stMap->Railways[PlineID].Powerends[i];
        if((trainonlinedirect==1) && (stMap->Powerends[ti].PowerEnd_type == trainonlinedirect)){
                   FKPoint tempPoint;
                   tempPoint.PointType=2;
                   tempPoint.PointID = stMap->Powerends[ti].PowerEnd_index;
                   strcpy(tempPoint.PointName,"终点标");
                   tempPoint.PointV = 0;
                   tempPoint.PointDis=(tempdis+stMap->Powerends[ti].linepos)*100;
                   KeyPointS->push_back(tempPoint);
        }else if((trainonlinedirect==2) && (stMap->Powerends[ti].PowerEnd_type == trainonlinedirect)){
            FKPoint tempPoint;
            tempPoint.PointType=2;
            tempPoint.PointID = stMap->Powerends[ti].PowerEnd_index;
            strcpy(tempPoint.PointName,"终点标");
            tempPoint.PointV = 0;
            tempPoint.PointDis=(tempdis+ stMap->Railways[PlineID].LineLength-stMap->Powerends[ti].linepos)*100;
            KeyPointS->push_back(tempPoint);
        }
    }
}

//确定当前线路机车前方的脱轨器
void FindDeraonLine(DStation *stMap, float trainPer, int PlineID, int trainonlinedirect,vector<FKPoint> *KeyPointS)
{
    for(int i =0; i<stMap->Railways[PlineID].totalDerailerN; i++){ //3 脱轨器
        USHORT ti =  stMap->Railways[PlineID].Derailers[i];
        if((trainonlinedirect==1) && ((stMap->Derailers[ti].Derailer_type == trainonlinedirect)||(stMap->Derailers[ti].Derailer_type ==0))
                                  && (trainPer<stMap->Derailers[ti].linepos) ){
                   FKPoint tempPoint;
                   tempPoint.PointType=3;
                   tempPoint.PointID = stMap->Derailers[ti].Derailer_index;
                   strcpy(tempPoint.PointName,"脱轨器");
                   tempPoint.PointV = 0;
                   tempPoint.PointDis=(stMap->Derailers[ti].linepos-trainPer)*100;
                   KeyPointS->push_back(tempPoint);
        }else if((trainonlinedirect==2) && ((stMap->Derailers[ti].Derailer_type == trainonlinedirect)||(stMap->Derailers[ti].Derailer_type ==0))
                                       && (trainPer>stMap->Derailers[ti].linepos)){
            FKPoint tempPoint;
            tempPoint.PointType=3;
            tempPoint.PointID = stMap->Derailers[ti].Derailer_index;
            strcpy(tempPoint.PointName,"脱轨器");
            tempPoint.PointV = 0;
            tempPoint.PointDis=(trainPer-stMap->Derailers[ti].linepos)*100;
            KeyPointS->push_back(tempPoint);
       }
    }
}
//查前方进路上的脱轨器
void FindDeraonRail(DStation *stMap, float tempdis, int PlineID, int trainonlinedirect,vector<FKPoint> *KeyPointS)
{
    for(int i =0; i<stMap->Railways[PlineID].totalDerailerN; i++){ //3 脱轨器
        USHORT ti =  stMap->Railways[PlineID].Derailers[i];
        if((trainonlinedirect==1) && ((stMap->Derailers[ti].Derailer_type == trainonlinedirect)||(stMap->Derailers[ti].Derailer_type ==0))){
                   FKPoint tempPoint;
                   tempPoint.PointType=3;
                   tempPoint.PointID = stMap->Derailers[ti].Derailer_index;
                   strcpy(tempPoint.PointName,"脱轨器");
                   tempPoint.PointV = 0;
                   tempPoint.PointDis=(tempdis+stMap->Derailers[ti].linepos)*100;
                   KeyPointS->push_back(tempPoint);
        }else if((trainonlinedirect==2) && ((stMap->Derailers[ti].Derailer_type == trainonlinedirect)||(stMap->Derailers[ti].Derailer_type ==0))){
            FKPoint tempPoint;
            tempPoint.PointType=3;
            tempPoint.PointID = stMap->Derailers[ti].Derailer_index;
            strcpy(tempPoint.PointName,"脱轨器");
            tempPoint.PointV = 0;
            tempPoint.PointDis=(tempdis+stMap->Railways[PlineID].LineLength-stMap->Derailers[ti].linepos)*100;
            KeyPointS->push_back(tempPoint);
       }
    }
}
//确定当前线路机车前方的站界
void FindStlionLine(DStation *stMap, float trainPer, int PlineID, int trainonlinedirect,vector<FKPoint> *KeyPointS)
{
    for(int i =0; i<stMap->Railways[PlineID].totalStLimitN; i++){ //5 站界
        USHORT ti =  stMap->Railways[PlineID].StLimits[i];
        if((trainonlinedirect==1) && (stMap->StLimits[ti].StLimit_type == trainonlinedirect)
                                  && (trainPer<stMap->StLimits[ti].linepos) ){
                   FKPoint tempPoint;
                   tempPoint.PointType=5;
                   tempPoint.PointID = stMap->StLimits[ti].StLimit_index;
                   strcpy(tempPoint.PointName,"站界");
                   tempPoint.PointV = 0;
                   tempPoint.PointDis=(stMap->StLimits[ti].linepos-trainPer)*100;
                   KeyPointS->push_back(tempPoint);

        }else if((trainonlinedirect==2) && (stMap->StLimits[ti].StLimit_type == trainonlinedirect)
                                       && (trainPer>stMap->StLimits[ti].linepos)){
            FKPoint tempPoint;
            tempPoint.PointType=5;
            tempPoint.PointID = stMap->StLimits[ti].StLimit_index;
            strcpy(tempPoint.PointName,"站界");
            tempPoint.PointV = 0;
            tempPoint.PointDis=(trainPer-stMap->StLimits[ti].linepos)*100;
            KeyPointS->push_back(tempPoint);
       }
    }
}
//查前方进路上机车前方站界
void FindStlionRail(DStation *stMap, float tempdis, int PlineID, int trainonlinedirect,vector<FKPoint> *KeyPointS)
{
    for(int i =0; i<stMap->Railways[PlineID].totalStLimitN; i++){ //5 站界
        USHORT ti =  stMap->Railways[PlineID].StLimits[i];
        if((trainonlinedirect==1) && (stMap->StLimits[ti].StLimit_type == trainonlinedirect)){
                   FKPoint tempPoint;
                   tempPoint.PointType=5;
                   tempPoint.PointID = stMap->StLimits[ti].StLimit_index;
                   strcpy(tempPoint.PointName,"站界");
                   tempPoint.PointV = 0;
                   tempPoint.PointDis=(tempdis+stMap->StLimits[ti].linepos)*100;
                   KeyPointS->push_back(tempPoint);
        }else if((trainonlinedirect==2) && (stMap->StLimits[ti].StLimit_type == trainonlinedirect)){
            FKPoint tempPoint;
            tempPoint.PointType=5;
            tempPoint.PointID = stMap->StLimits[ti].StLimit_index;
            strcpy(tempPoint.PointName,"站界");
            tempPoint.PointV = 0;
            tempPoint.PointDis=(tempdis+stMap->Railways[PlineID].LineLength-stMap->StLimits[ti].linepos)*100;
            KeyPointS->push_back(tempPoint);
       }
    }
}

//确定当前线路机车前方的土挡
void FindStoponLine(DStation *stMap, float trainPer, int PlineID, int trainonlinedirect,vector<FKPoint> *KeyPointS)
{
    for(int i =0; i<stMap->Railways[PlineID].totalStoppointN; i++){ //4 土挡
        USHORT ti =  stMap->Railways[PlineID].Stoppoints[i];
        if((trainonlinedirect==1) && (stMap->Stoppoints[ti].Stype == trainonlinedirect)
                                  && (trainPer<stMap->Stoppoints[ti].line_pos) ){
                   FKPoint tempPoint;
                   tempPoint.PointType=4;
                   tempPoint.PointID = stMap->Stoppoints[ti].s_id;
                   strcpy(tempPoint.PointName,stMap->Stoppoints[ti].Stoppoint_name);
                   tempPoint.PointV = 0;
                   tempPoint.PointDis=(stMap->Stoppoints[ti].line_pos-trainPer)*100;
                   KeyPointS->push_back(tempPoint);
        }else if((trainonlinedirect==2) && (stMap->Stoppoints[ti].Stype == trainonlinedirect)
                                       && (trainPer>stMap->Stoppoints[ti].line_pos)){
            FKPoint tempPoint;
            tempPoint.PointType=4;
            tempPoint.PointID = stMap->Stoppoints[ti].s_id;
            strcpy(tempPoint.PointName,stMap->Stoppoints[ti].Stoppoint_name);
            tempPoint.PointV = 0;
            tempPoint.PointDis=(trainPer-stMap->Stoppoints[ti].line_pos)*100;
            KeyPointS->push_back(tempPoint);
       }
    }
}
//查前方进路上的土挡
void FindStoponRail(DStation *stMap, float tempdis, int PlineID, int trainonlinedirect,vector<FKPoint> *KeyPointS)
{
    for(int i =0; i<stMap->Railways[PlineID].totalStoppointN; i++){ //4 土挡
        USHORT ti =  stMap->Railways[PlineID].Stoppoints[i];
        if((trainonlinedirect==1) && (stMap->Stoppoints[ti].Stype == trainonlinedirect)){
                   FKPoint tempPoint;
                   tempPoint.PointType=4;
                   tempPoint.PointID = stMap->Stoppoints[ti].s_id;
                   strcpy(tempPoint.PointName,stMap->Stoppoints[ti].Stoppoint_name);
                   tempPoint.PointV = 0;
                   tempPoint.PointDis=(tempdis+stMap->Stoppoints[ti].line_pos)*100;
                   KeyPointS->push_back(tempPoint);
        }else if((trainonlinedirect==2) && (stMap->Stoppoints[ti].Stype == trainonlinedirect)){
            FKPoint tempPoint;
            tempPoint.PointType=4;
            tempPoint.PointID = stMap->Stoppoints[ti].s_id;
            strcpy(tempPoint.PointName,stMap->Stoppoints[ti].Stoppoint_name);
            tempPoint.PointV = 0;
            tempPoint.PointDis=(tempdis+stMap->Railways[PlineID].LineLength-stMap->Stoppoints[ti].line_pos)*100;
            KeyPointS->push_back(tempPoint);
       }
    }
}

//确定当前线路机车前方的无网线路
void  FindWwquonLine(DStation *stMap, float trainPer, int PlineID, int trainonlinedirect,vector<FKPoint> *KeyPointS)
{
    if (trainonlinedirect == 1) {
        for (int i = 0; i < stMap->Railways[PlineID].nextLineN; i++) {
            USHORT ti= stMap->Railways[PlineID].nextLineIDs[i];
            if (stMap->Railways[ti].NonPowerStat == 1) {
                FKPoint tempPoint;
                tempPoint.PointType=7;
                tempPoint.PointID = ti;
                strcpy(tempPoint.PointName,"无网区");
                tempPoint.PointV = 0;
                tempPoint.PointDis=(stMap->Railways[PlineID].LineLength-trainPer)*100;
                KeyPointS->push_back(tempPoint);
            }
        }
    }
    else if (trainonlinedirect == 2) {
        for (int i = 0; i < stMap->Railways[PlineID].preLineN; i++) {
            USHORT ti= stMap->Railways[PlineID].preLineIDs[i];
            if (stMap->Railways[ti].NonPowerStat == 1) {
                FKPoint tempPoint;
                tempPoint.PointType=7;
                tempPoint.PointID = ti;
                strcpy(tempPoint.PointName,"无网区");
                tempPoint.PointV = 0;
                tempPoint.PointDis=trainPer*100;
                KeyPointS->push_back(tempPoint);
            }
        }
    }
}
//查前方进路上的无网线路
void  FindWwquonRail(DStation *stMap, float tempdis, int PlineID, int trainonlinedirect,vector<FKPoint> *KeyPointS)
{
    if (trainonlinedirect == 1) {
        for (int i = 0; i < stMap->Railways[PlineID].nextLineN; i++) {
            USHORT ti= stMap->Railways[PlineID].nextLineIDs[i];
            if (stMap->Railways[ti].NonPowerStat == 1) {
                FKPoint tempPoint;
                tempPoint.PointType=7;
                tempPoint.PointID = ti;
                strcpy(tempPoint.PointName,"无网区");
                tempPoint.PointV = 0;
                tempPoint.PointDis=(tempdis+stMap->Railways[PlineID].LineLength)*100;
                KeyPointS->push_back(tempPoint);
            }
        }
    }
    else if (trainonlinedirect == 2) {
        for (int i = 0; i < stMap->Railways[PlineID].preLineN; i++) {
            USHORT ti= stMap->Railways[PlineID].preLineIDs[i];
            if (stMap->Railways[ti].NonPowerStat == 1) {
                FKPoint tempPoint;
                tempPoint.PointType=7;
                tempPoint.PointID = ti;
                strcpy(tempPoint.PointName,"无网区");
                tempPoint.PointV = 0;
                tempPoint.PointDis=(tempdis+stMap->Railways[PlineID].LineLength)*100;
                KeyPointS->push_back(tempPoint);
            }
        }
    }
}

//确定当前线路机车前方的特殊防控项点
void FindSpeconLine(DStation *stMap, float trainPer, int PlineID, int trainonlinedirect,vector<FKPoint> *KeyPointS)
{
    for(int i =0; i<stMap->Railways[PlineID].totalSPN; i++){ //8 特殊防控项点
        USHORT ti =  stMap->Railways[PlineID].SpecPoints[i];
        if((trainonlinedirect==1) && (stMap->SpecPoints[ti].sp_type == trainonlinedirect)
                                  && (trainPer<stMap->SpecPoints[ti].linepos) ){
                   FKPoint tempPoint;
                   tempPoint.PointType=8;
                   tempPoint.PointID = stMap->SpecPoints[ti].sp_index;
                   strcpy(tempPoint.PointName,stMap->SpecPoints[ti].SPName);
                   tempPoint.PointV = 0;
                   tempPoint.PointDis=(stMap->SpecPoints[ti].linepos-trainPer)*100;
                   KeyPointS->push_back(tempPoint);
        }else if((trainonlinedirect==2) && (stMap->SpecPoints[ti].sp_type == trainonlinedirect)
                                       && (trainPer>stMap->SpecPoints[ti].linepos)){
            FKPoint tempPoint;
            tempPoint.PointType=8;
            tempPoint.PointID = stMap->SpecPoints[ti].sp_index;
            strcpy(tempPoint.PointName,stMap->SpecPoints[ti].SPName);
            tempPoint.PointV = 0;
            tempPoint.PointDis=(trainPer-stMap->SpecPoints[ti].linepos)*100;
            KeyPointS->push_back(tempPoint);
       }
    }
}
//查前方进路上的特殊防控项点
void FindSpeconRail(DStation *stMap, float tempdis, int PlineID, int trainonlinedirect,vector<FKPoint> *KeyPointS)
{
    for(int i =0; i<stMap->Railways[PlineID].totalSPN; i++){ //8 特殊防控项点
        USHORT ti =  stMap->Railways[PlineID].SpecPoints[i];
        if((trainonlinedirect==1) && (stMap->SpecPoints[ti].sp_type == trainonlinedirect)){
                   FKPoint tempPoint;
                   tempPoint.PointType=8;
                   tempPoint.PointID = stMap->SpecPoints[ti].sp_index;
                   strcpy(tempPoint.PointName,stMap->SpecPoints[ti].SPName);
                   tempPoint.PointV = 0;
                   tempPoint.PointDis=(tempdis + stMap->SpecPoints[ti].linepos)*100;
                   KeyPointS->push_back(tempPoint);
        }else if((trainonlinedirect==2) && (stMap->SpecPoints[ti].sp_type == trainonlinedirect)){
            FKPoint tempPoint;
            tempPoint.PointType=8;
            tempPoint.PointID = stMap->SpecPoints[ti].sp_index;
            strcpy(tempPoint.PointName,stMap->SpecPoints[ti].SPName);
            tempPoint.PointV = 0;
            tempPoint.PointDis=(tempdis+stMap->Railways[PlineID].LineLength-stMap->SpecPoints[ti].linepos)*100;
            KeyPointS->push_back(tempPoint);
       }
    }
}

//确定当前线路机车前方的禁停区
void FindNPAonLine(DStation *stMap, float trainPer, int PlineID, int trainonlinedirect,vector<FKPoint> *KeyPointS)
{
    for(int i =0; i<stMap->Railways[PlineID].totalNPAN; i++){ //9 禁停区
        USHORT ti =  stMap->Railways[PlineID].NPAs[i];
        if((trainonlinedirect==1)  && (trainPer<stMap->NPAs[ti].begin_linepos) ){
                   FKPoint tempPoint;
                   tempPoint.PointType=9;
                   tempPoint.PointID = stMap->NPAs[ti].NPA_index;
                   strcpy(tempPoint.PointName,"禁停区");
                   tempPoint.PointV = 0;
                   tempPoint.PointDis=(stMap->NPAs[ti].begin_linepos-trainPer)*100;
                   KeyPointS->push_back(tempPoint);
        }else if((trainonlinedirect==2)  && (trainPer>stMap->NPAs[ti].end_linepos)){
            FKPoint tempPoint;
            tempPoint.PointType=9;
            tempPoint.PointID = stMap->NPAs[ti].NPA_index;
            strcpy(tempPoint.PointName,"禁停区");
            tempPoint.PointV = 0;
            tempPoint.PointDis=(trainPer-stMap->NPAs[ti].end_linepos)*100;
            KeyPointS->push_back(tempPoint);
        }else if(trainPer<=stMap->NPAs[ti].end_linepos && trainPer>=stMap->NPAs[ti].begin_linepos){
            FKPoint tempPoint;
            tempPoint.PointType=9;
            tempPoint.PointID = stMap->NPAs[ti].NPA_index;
            strcpy(tempPoint.PointName,"禁停区");
            tempPoint.PointV = 0;
            tempPoint.PointDis=0;
            KeyPointS->push_back(tempPoint);
        }
    }

}

//查前方进路上的禁停区
void FindNPAonRail(DStation *stMap, float tempdis, int PlineID, int trainonlinedirect,vector<FKPoint> *KeyPointS)
{
    for(int i =0; i<stMap->Railways[PlineID].totalNPAN; i++){ //9 禁停区
        USHORT ti =  stMap->Railways[PlineID].NPAs[i];
        if(trainonlinedirect==1){
           FKPoint tempPoint;
           tempPoint.PointType=9;
           tempPoint.PointID = stMap->NPAs[ti].NPA_index;
           strcpy(tempPoint.PointName,"禁停区");
           tempPoint.PointV = 0;
           tempPoint.PointDis=(tempdis+stMap->NPAs[ti].begin_linepos)*100;
           KeyPointS->push_back(tempPoint);
        }else if(trainonlinedirect==2){
            FKPoint tempPoint;
            tempPoint.PointType=9;
            tempPoint.PointID = stMap->NPAs[ti].NPA_index;
            strcpy(tempPoint.PointName,"禁停区");
            tempPoint.PointV = 0;
            tempPoint.PointDis=(tempdis + stMap->Railways[PlineID].LineLength-stMap->NPAs[ti].end_linepos)*100;
            KeyPointS->push_back(tempPoint);
        }
    }

}

//确定机车上下行状态
char Get_Line_xb(DStation *stMap, int PlineID, int trainonlinedirect)
{
   char stat=2;
   if(trainonlinedirect==1){
       if(stMap->Railways[PlineID].up_type==1)//上行
                  stat=0;
       else if(stMap->Railways[PlineID].up_type==2)//下行
                  stat=1;
   }else if(trainonlinedirect==2){
       if(stMap->Railways[PlineID].up_type==1)//上行
                  stat=1;
       else if(stMap->Railways[PlineID].up_type==2)//下行
                  stat=0;

   }
   return stat;
}

//确定机车上下行状态
uint32_t Get_DisToDC(DStation *stMap, int PlineID, float per,int trainonlinedirect)
{
   uint32_t stat=2;
   if(trainonlinedirect==1){
        stat=(stMap->Railways[PlineID].LineLength-per)*100;
   }else if(trainonlinedirect==2){
        stat=per*100;
   }else if(trainonlinedirect==0){
        stat = per*100; 
   }
   return stat;
}

//确定机车前方进路和防控项
//确定机车前方进路和防控项点,存入全局变量中的进路容器和防控项点容器
void FindtheWayandPoint(DStation *stMap, float trainPer, int PlineID, int trainonlinedirect, vector<FKPoint> *KeyPointS,vector<Railway> *KeyLineS)
{
  
   KeyPointS->clear();
   KeyLineS->clear();

   uint32_t dcds; 
   //当前线路压入容器
   Railway tempway;
   tempway.RailwayID = PlineID;
   tempway.RailLength = stMap->Railways[PlineID].LineLength*100;
   tempway.xb =Get_Line_xb(stMap,PlineID,trainonlinedirect);
   tempway.DisToDC = Get_DisToDC(stMap, PlineID, trainPer,trainonlinedirect);
   tempway.MaxSpeed =40;
   tempway.direct = trainonlinedirect;
   tempway.xb = trainonlinedirect;
   strcpy(tempway.RailwayName,stMap->Railways[PlineID].line_name);
   KeyLineS->push_back(tempway);
   dcds=tempway.DisToDC;

   if(trainonlinedirect==0) return;//机车方向不定，返回退出

   //查找当前线路的防控项点
   FindLamponLine(stMap, trainPer, PlineID, trainonlinedirect,KeyPointS);//1 信号灯
   FindPwndonLine(stMap, trainPer, PlineID, trainonlinedirect,KeyPointS);//2 接触网终点标
   FindDeraonLine(stMap, trainPer, PlineID, trainonlinedirect,KeyPointS);//3 脱轨器
   FindStlionLine(stMap, trainPer, PlineID, trainonlinedirect,KeyPointS);//5 站界
   FindStoponLine(stMap, trainPer, PlineID, trainonlinedirect,KeyPointS);//4 土挡
   FindWwquonLine(stMap, trainPer, PlineID, trainonlinedirect,KeyPointS);//7 无网线路
   FindSpeconLine(stMap, trainPer, PlineID, trainonlinedirect,KeyPointS);//8 特殊防控项点
   FindNPAonLine(stMap,  trainPer, PlineID, trainonlinedirect,KeyPointS);//9 禁停区


   //检查本线路外是否有防控项点
   int nextID = -1;
   float ptx, pty;
   float tempdis = 0;
   float  temp_train_head = 0;
   if ((stMap->Railways[PlineID].nextLineN == 1) && (trainonlinedirect == 1)) {  // 正向
       ptx = stMap->Railways[PlineID].Points[stMap->Railways[PlineID].totalPointsN - 1].x;
       pty = stMap->Railways[PlineID].Points[stMap->Railways[PlineID].totalPointsN - 1].y;
       nextID = stMap->Railways[PlineID].nextLineIDs[0];
       tempdis= stMap->Railways[PlineID].LineLength-trainPer;
   }
   else if ((stMap->Railways[PlineID].preLineN == 1) && (trainonlinedirect == 2)) {// 反向
       ptx = stMap->Railways[PlineID].Points[0].x;
       pty = stMap->Railways[PlineID].Points[0].y;
       nextID = stMap->Railways[PlineID].preLineIDs[0];
       tempdis= trainPer;
   }

   if (nextID == -1) {//前方无进路
      //进路上的防控项点按距离近远排序
     sort(KeyPointS->begin(), KeyPointS->end(), [](const FKPoint& a, const FKPoint& b) {
        return a.PointDis < b.PointDis;
     });
     return ;
   }

   while (1) {//检查前方进路上的防控项点
      int pn = stMap->Railways[nextID].totalPointsN - 1;
      float x_0 = stMap->Railways[nextID].Points[0].x;
      float y_0 = stMap->Railways[nextID].Points[0].y;
      float x_n = stMap->Railways[nextID].Points[pn].x;
      float y_n = stMap->Railways[nextID].Points[pn].y;
      float d_0 = (x_0 - ptx) * (x_0 - ptx) + (y_0 - pty) * (y_0 - pty);
      float d_n = (x_n - ptx) * (x_n - ptx) + (y_n - pty) * (y_n - pty);
      if (d_0 < d_n)
          temp_train_head = 1;
      else if (d_0 > d_n)
          temp_train_head = 2;

      //进路压入容器al.cpp:1810:92: error: cannot convert ‘DStation {aka 
      Railway nextline;
      nextline.RailwayID = nextID;
      nextline.RailLength = stMap->Railways[nextID].LineLength*100;
      nextline.xb =Get_Line_xb(stMap,nextID,temp_train_head);
      nextline.DisToDC = nextline.RailLength+dcds;
      dcds=nextline.DisToDC;
      nextline.MaxSpeed =1999;
      nextline.direct = temp_train_head;
      nextline.xb = temp_train_head;
      strcpy(nextline.RailwayName,stMap->Railways[nextID].line_name);
      KeyLineS->push_back(nextline);


      FindLamponRail(stMap, tempdis, nextID, temp_train_head,KeyPointS);//1 信号灯
      FindPwndonRail(stMap, tempdis, nextID, temp_train_head,KeyPointS);//2 接触网终点标
      FindDeraonRail(stMap, tempdis, nextID, temp_train_head,KeyPointS);//3 脱轨器
      FindStlionRail(stMap, tempdis, nextID, temp_train_head,KeyPointS);//5 站界
      FindStoponRail(stMap, tempdis, nextID, temp_train_head,KeyPointS);//4 土挡
      FindWwquonRail(stMap, tempdis, nextID, temp_train_head,KeyPointS);//7 无网线路
      FindSpeconRail(stMap, tempdis, nextID, temp_train_head,KeyPointS);//8 特殊防控项点
      FindNPAonRail(stMap,  tempdis, nextID, temp_train_head,KeyPointS);//9 禁停区

      if (temp_train_head == 1) {//如果正向

          if (stMap->Railways[nextID].nextLineN == 1) {
              tempdis = tempdis +stMap->Railways[nextID].LineLength;
              ptx = stMap->Railways[nextID].Points[stMap->Railways[nextID].totalPointsN - 1].x;
              pty = stMap->Railways[nextID].Points[stMap->Railways[nextID].totalPointsN - 1].y;
              nextID = stMap->Railways[nextID].nextLineIDs[0];
          }
          else {
              break;
          }
      }
      else if (temp_train_head == 2) {//如果反向
          if (stMap->Railways[nextID].preLineN == 1) {
              tempdis = tempdis + stMap->Railways[nextID].LineLength;
              ptx = stMap->Railways[nextID].Points[0].x;
              pty = stMap->Railways[nextID].Points[0].y;
              nextID = stMap->Railways[nextID].preLineIDs[0];
          }
          else {
              break;
          }
      }
   }
   //进路上的防控项点按距离近远排序
   sort(KeyPointS->begin(), KeyPointS->end(), [](const FKPoint& a, const FKPoint& b) {
       return a.PointDis < b.PointDis;
     });
}

//计算机车距轨道起点的距离，单位米
float find_disp_to_begin(DStation *stMap, DSPoint trainPos, int PlineID, int trainindex)
{
    int last_i = -1;
    float dis = 0;
    float lx, ly, dx, dy;
    float txx,tyy;
    txx=trainPos.x;
    tyy=trainPos.y;

    if (PlineID < 0) {
        return -1;
    }

    lx = stMap->Railways[PlineID].Points[0].x;
    ly = stMap->Railways[PlineID].Points[0].y;

    last_i = trainindex;

    if (last_i == 0) {
        dx = txx - lx;
        dy = tyy - ly;
        dis = sqrt(dx * dx + dy * dy);
    }
    else if(last_i >0){
        dis = 0;
        for (int i = 0;i < last_i;i++) {
            float x1 = stMap->Railways[PlineID].Points[i].x;
            float y1 = stMap->Railways[PlineID].Points[i].y;
            float x2 = stMap->Railways[PlineID].Points[i + 1].x;
            float y2 = stMap->Railways[PlineID].Points[i + 1].y;
            dx = x1 - x2;
            dy = y1 - y2;
            dis=dis+ sqrt(dx * dx + dy * dy);
        }
        dx = txx - stMap->Railways[PlineID].Points[last_i].x;
        dy = tyy - stMap->Railways[PlineID].Points[last_i].y;
        dis = dis + sqrt(dx * dx + dy * dy);
    }
    else {
        dis = -1;
    }
    if (dis == 0) {
        dis = 0;
    }

    return dis;
}


//经纬度转84投影坐标
void GetXYfromUDP(int Longitude,int Latitude,double *tx, double *ty)
{
	double B;        //纬度度数
	double L;        //经度度数
	double L0;        //中央经线度数
	double l;        //L-L0
	double t;        //tanB
	double m;        //ltanB
	double N;        //卯酉圈曲率半径 
	double q2;
	double x;        //高斯平面纵坐标
	double y;        //高斯平面横坐标
	double s;        //赤道至纬度B的经线弧长
	double f;        //参考椭球体扁率
	double e2;        //椭球第一偏心率
	double a;        //参考椭球体长半轴
	//double b;        //参考椭球体短半轴
	double a1;
	double a2;
	double a3;
	double a4;
	double b1;
	double b2;
	double b3;
	double b4;
	double c0;
	double c1;
	double c2;
	double c3;
	int Datum = 84;       //投影基准面类型：北京54基准面为54，西安80基准面为80，WGS84基准面为84
	int prjno = 0;        //投影带号
	int zonewide = 3;
	double IPI = 0.0174532925199433333333;        //3.1415926535898/180.0
	B = Latitude/10000000.0; //纬度
	L = Longitude/10000000.0; //经度
	if (zonewide == 6)
	{
		prjno = (int)(L / zonewide) + 1;
		L0 = prjno*zonewide - 3;
	}
	else
	{
		prjno = (int)((L - 1.5) / 3) + 1;
		L0 = prjno * 3;
	}

	if (Datum == 54)
	{
		a = 6378245;
		f = 1 / 298.3;
	}
	else if (Datum == 84)
	{
		a = 6378137;
		f = 1 / 298.257223563;
	}

	if (L > 112.51 && L < 112.57) //针对太原北站增加
		L0 = 111;                 //针对太原北站增加 
	
	L0 = L0*IPI;
	L = L*IPI;
	B = B*IPI;

	e2 = 2 * f - f*f;//(a*a-b*b)/(a*a);
	l = L - L0;
	t = tan(B);
	m = l * cos(B);
	N = a / sqrt(1 - e2* sin(B) * sin(B));
	q2 = e2 / (1 - e2)* cos(B)* cos(B);
	a1 = 1 + (double)3 / 4 * e2 + (double)45 / 64 * e2*e2 + (double)175 / 256 * e2*e2*e2 + (double)11025 / 16384 * e2*e2*e2*e2 + (double)43659 / 65536 * e2*e2*e2*e2*e2;
	a2 = (double)3 / 4 * e2 + (double)15 / 16 * e2*e2 + (double)525 / 512 * e2*e2*e2 + (double)2205 / 2048 * e2*e2*e2*e2 + (double)72765 / 65536 * e2*e2*e2*e2*e2;
	a3 = (double)15 / 64 * e2*e2 + (double)105 / 256 * e2*e2*e2 + (double)2205 / 4096 * e2*e2*e2*e2 + (double)10359 / 16384 * e2*e2*e2*e2*e2;
	a4 = (double)35 / 512 * e2*e2*e2 + (double)315 / 2048 * e2*e2*e2*e2 + (double)31185 / 13072 * e2*e2*e2*e2*e2;
	b1 = a1*a*(1 - e2);
	b2 = (double)-1 / 2 * a2*a*(1 - e2);
	b3 = (double)1 / 4 * a3*a*(1 - e2);
	b4 = (double)-1 / 6 * a4*a*(1 - e2);
	c0 = b1;
	c1 = 2 * b2 + 4 * b3 + 6 * b4;
	c2 = -(8 * b3 + 32 * b4);
	c3 = 32 * b4;
	s = c0*B + cos(B)*(c1*sin(B) + c2*sin(B)*sin(B)*sin(B) + c3*sin(B)*sin(B)*sin(B)*sin(B)*sin(B));
	x = s + (double)1 / 2 * N*t*m*m + (double)1 / 24 * (5 - t*t + 9 * q2 + 4 * q2*q2)*N*t*m*m*m*m + (double)1 / 720 * (61 - 58 * t*t + t*t*t*t)*N*t*m*m*m*m*m*m;
	y = N*m + (double)1 / 6 * (1 - t*t + q2)*N*m*m*m + (double)1 / 120 * (5 - 18 * t*t + t*t*t*t - 14 * q2 - 58 * q2*t*t)*N*m*m*m*m*m;

	//y = y + 1000000 * prjno + 500000;
	//pcc.x = x;
	//pcc.y = y - 38000000;
	//pcc.z = pcg.height;

	y = y  + 500000;
	*ty = x;
	*tx = y;
}

void SendBuftoCMS(byte *bbuf,int tlen,int mode,uint16_t frameI)
{
   byte sBuf[2048]={0};
   ForwBuf obj;
   obj.buf[0]=0xFA;
   obj.buf[1]=0xFA;
   obj.buf[2]=mode;//是否压缩
   int slen=8+tlen;
   obj.buf[3]=slen;
   obj.buf[4]=slen >> 8;
   obj.buf[5]=frameI;
   obj.buf[6]=frameI >>8 ;
   obj.len=slen;
   for(int i=0;i<tlen;i++){
      obj.buf[7+i]=bbuf[i];
   }

   for(int i=0;i<slen-1;i++){
     obj.buf[slen-1]=obj.buf[slen-1]+ obj.buf[i];
   }
   G_V.mtxudptoCMSQueue.lock();
   G_V.udptoCMSQueue.push(obj);
   G_V.mtxudptoCMSQueue.unlock();
}

//获取铁路局名称
void GetTLJName(int TLJ,char *TLJ_Name)
{
       
}
//获取站场名称
void GetStatName(int station_id,char *Station_name)
{
    
}
//从LSP发送的信息中提取数据
void GetDBSInfoFromLSP(DBSaveInfo* dbsinfo, LSPDat* lspdat) 
{
    dbsinfo->TypeID=*(uint16_t *) &(lspdat->lspBase.TrainNumber[0]);
    dbsinfo->TrainN=*(uint32_t *) &(lspdat->lspBase.TrainNumber[2]);
    dbsinfo->FactoryID = lspdat->lspBase.FactoryID;
    dbsinfo->TLJ=lspdat->lspBase.TLJ;
    dbsinfo->JWD=lspdat->lspBase.Sectionno;
    struct tm *ptm;
    time_t TimeStamp;
    TimeStamp=lspdat->lspBase.TimeStamp;
    ptm=localtime(&TimeStamp);
    strftime(dbsinfo->DateTime, sizeof(dbsinfo->DateTime), "%Y-%m-%d %H:%M:%S", ptm);
    dbsinfo->Longitude=lspdat->lspBDS.Longitude; 
    dbsinfo->Latitude=lspdat->lspBDS.Latitude;   
    dbsinfo->Speed = lspdat->lspLKJ.Speed;
    dbsinfo->TrainDirect =lspdat->lspLKJ.TrainDirect;
    dbsinfo->TrainCondition =lspdat->lspLKJ.TrainCondition;
    dbsinfo->EquipStatus = lspdat->lspLKJ.EquipStatus;
    dbsinfo->KHBB =lspdat->lspLKJ.KHBB;
    //strcpy(dbsinfo->TrainNumber,lspdat->lspLKJ.TrainNumber);
    for(int i=0;i<8;i++)
    {
        dbsinfo->TrainNumber[i]=lspdat->lspLKJ.TrainNumber[i];
    }
    dbsinfo->ErrorNumber = lspdat->lspLKJ.ErrorNumber;
    dbsinfo->EventType = lspdat->lspLKJ.space[0];
}

//根据机车运行方向，车头方向和天线位置对距离进行修正
float DistModify(float per,int linedirect,TAXMSG *train_pam) 
{
  float res;//修正后的机车和线路起点间的距离
  res=per;
  if(train_pam->TrainDirect==1)//车头向前
  {
    if(linedirect==1){//机车运行方向 起点-->终点
       res=per+train_pam->DistanceA/100.0;
       
    }else if(linedirect==2)//机车运行方向 终点-->起点
    {
       res=per-train_pam->DistanceA/100.0; 
    }
  
  }else if(train_pam->TrainDirect==16){//车尾向前
    if(linedirect==1){//机车运行方向 起点-->终点
         res=per+train_pam->DistanceB/100.0;
    }else if(linedirect==2)//机车运行方向 终点-->起点
    {
        res=per-train_pam->DistanceB/100.0;
    }
  }
  return  res;
}

//地算线程，每车1个
void *DS_Cal_For_lsp(void *lpPara)
{
   char TLJName[32];
   char Station_Name[64];
   //char TrainType[32];
   int  TrainNumber;
   //char TrainName[64];
   long t1,t2;
   uint16_t frameN=0;
   uint64_t MSGID,pre_MSGID=0;
   int pre_StationID=-1,pre_TLJID=-1;
   DStation curStation;                 //当前站场地图
   int  curLineID =-1, preLineID=-1;    //当前,上一次股道编号
   int  cur_foot_index=-1;              //当前gps天线所在区间
   DSPoint  curTrainPos;                //当前北斗天线投影坐标
   DSPoint  trainFoot,trainSP;          //机车在股道垂足
   int trainonlinedirect=0,pre_trainonlinedirect;    //机车运行方向
                                                     //  1 : 从线路起点向终点运行
                                                     //  2 : 从线路终点向起点运行
   int train_line_direct[10] = { 0 };   
   float pre_LinePer[5]={0};                                               
   SelLine closeLine[3];                     //保存求出的距离机车最近的3条股道   
   byte                   KeyPoint_N;        //防控项点数量
   vector<FKPoint>        KeyPointS;        //防控项点容器
                                             //第1个为机车当前所面对的项点，余下为机车需要依次面对的项点
   byte                    KeyLine_N;        //进路股道数量
   vector<Railway>          KeyLineS;        //进路股道容器
                                             //第1条为机车所在股道，余下为机车需要依次通过的股道
   int preDiaoCheStat=0,curDiaoCheStat=0;    //是否调车
   
   int line_change_waittime_ifneeded=0;
   int inlinedataid = 0;

   bool showmap_stat=true;      //控制地图是否显示

   int q_id;
   q_id=*(int *)lpPara;
   t1= what_time_is_it_now();
   double tx,ty;

   ////////////////////////////////////////////////////////////////////
   int  socket_send;
   socket_send=socket(AF_INET,SOCK_DGRAM,0); //USE UDP
   //保存数据发送地址
   struct sockaddr_in DBSAddr[2];
   for(int i=0;i<2;i++){
        memset(&DBSAddr[i], 0, sizeof(sockaddr_in));
        DBSAddr[i].sin_family = AF_INET;
        DBSAddr[i].sin_port = htons(20001);
        DBSAddr[i].sin_addr.s_addr = inet_addr(G_V.DBIP[i]);
   }
   ////////////////////////////////////////////////////////////////////
   long preMGID =0,curMSGID=0;
   LSPDat udp_dat;
   uint16_t TypeID;
   uint32_t TrainN;
   while(1)
   {
       if(G_V.lspQue[q_id].cmsdatQue.empty()){
            t2= what_time_is_it_now();
            if((t2-t1)>300000){ //超过5分钟没有受到数据，线程退出
                break;
            }else    
                continue;             
       }else{
            G_V.lspQue[q_id].queMutex.lock();
            udp_dat=G_V.lspQue[q_id].cmsdatQue.front();
            G_V.lspQue[q_id].cmsdatQue.pop();  
            G_V.lspQue[q_id].queMutex.unlock();          
            t1= what_time_is_it_now();
       }

       preMGID = curMSGID;
       curMSGID=udp_dat.lspBDS.MSGID;
       if(pre_MSGID>curMSGID) continue;
       
       //获取车型车号、车号
       TypeID=*(uint16_t *)&udp_dat.lspBase.TrainNumber[0];
       TrainN=*(uint32_t *)&udp_dat.lspBase.TrainNumber[2];
       //GetTrainType(TypeID,TrainType);
       //sprintf(TrainName,"%s-%d%c",TrainType,TrainN,udp_dat.lspBase.TrainNumber[6]);

       if(pre_StationID != udp_dat.stationID){//加载站场图
           pre_StationID = udp_dat.stationID; 
           pre_TLJID = udp_dat.TLJ;
           if(!LoadStation_Map(pre_StationID,pre_TLJID,&curStation)){//加载当前站场地图
               char tempbuf[128];
               sprintf(tempbuf,"读取地图文件[%d.bds]失败",pre_StationID);
               rk_syslog(tempbuf);
               continue;
           }
           strcpy(TLJName,curStation.areaName);
           strcpy(Station_Name,curStation.stationName);
       }
       
       if(udp_dat.lspBDS.MSGID >pre_MSGID){
            pre_MSGID = udp_dat.lspBDS.MSGID;
       }else{
            continue;
       }

       //选择网络通道
       int netid=-1;
       int gd_index=-1;
       if(G_V.CMS_Stat[0])
             netid=0;
       else if(!G_V.CMS_Stat[0] && G_V.CMS_Stat[1]) 
             netid=1;

       if(netid !=-1) gd_index=GetGdzdhID(pre_StationID,netid);

       if(gd_index>=0){//处理股道自动化信息
           G_V.Mutex_gdzdh[netid][gd_index].lock();
           GetGdzdhInfo(G_V.jwdBuf[netid][gd_index].jwdbuf, G_V.jwdBuf[netid][gd_index].bufLen,&curStation);
           G_V.Mutex_gdzdh[netid][gd_index].unlock();
           GetRailPath(&curStation);
       }else if(gd_index==-2){//股道自动化信息延迟无效，恢复线路、道岔和信号灯初始状态
            RestoreRailPath(&curStation); 
       } 

       GetXYfromUDP(udp_dat.lspBDS.Longitude,udp_dat.lspBDS.Latitude,&tx,&ty);
       curTrainPos.x = tx;
       curTrainPos.y = ty;

       //开始定位火车位置
       float min_ds;
       int min_i;
       //运行前方仅有1条股道，放大控制误差
       float tempds;
       tempds = LOCO_DISTEN;
       int linechanged=0;
       if(curLineID >= 0 ){
           min_ds = find_d_toMulPline(&curStation, curStation.Railways[curLineID].PlineID,
                                      curTrainPos.x, curTrainPos.y,
                                      &cur_foot_index, &trainFoot.x, &trainFoot.y);

           if ((curStation.Railways[curLineID].nextLineN == 1 && trainonlinedirect == 1 )
               || (curStation.Railways[curLineID].preLineN == 1 && trainonlinedirect == 2)) {
               tempds = 3 * LOCO_DISTEN;
           }
           if(min_ds >tempds || min_ds<0) {
               linechanged=1;
           }else{
              min_i= curLineID;
              linechanged=0;
            }
       }
       if((linechanged==1) ||((curLineID==-1)&&(linechanged==0))){
           min_i=GetTheClosestLineToTheTrain(&curStation,curTrainPos,&min_ds, &cur_foot_index,
                                               &trainFoot.x, &trainFoot.y,closeLine);
           if(min_ds >tempds || min_i==-1) {
               curLineID=-1;
               inlinedataid = 0;
               trainonlinedirect = 0;
               SetTrainDirect(train_line_direct,0);               
               continue;
           }
       }
       
       if(cur_foot_index < 0)  continue;     
       //如果定位出的股道编号和当前股道编号不一致，检查股道的切换是否合理
       if (curLineID != curStation.Railways[min_i].PlineID && curLineID !=-1){
           int checkResult=CheckTheLineIDChange(&curStation, curTrainPos,curLineID, trainonlinedirect,
                                &cur_foot_index, &trainFoot.x, &trainFoot.y,&line_change_waittime_ifneeded,closeLine);
           if (checkResult == -1) {//不合理，跳出计算
               preLineID = -1;
               curLineID = -1;
               trainonlinedirect = 0;
               SetTrainDirect(train_line_direct,0);
               inlinedataid = 0;
               continue;
           }
           else if (checkResult == -2) {//等待
               preLineID = curLineID;
        
               continue;
           }
           else if (checkResult == 1) {//切换股道，初始化相关状态参数
               preLineID = curLineID;
               curLineID = curStation.Railways[min_i].PlineID;
               //切换线路时，初始化相关状态参数
               showmap_stat = true;
               pre_trainonlinedirect = trainonlinedirect;
               trainonlinedirect = GetTrainDirectInitial(&curStation, preLineID,curLineID);//根据前后股道的顺序确定机车在切换股道上的初始方向
               SetTrainDirect(train_line_direct,trainonlinedirect);
               inlinedataid = 0;
               if(trainonlinedirect==1)
                  SetLinePer(pre_LinePer,0);
               else
                  SetLinePer(pre_LinePer,curStation.Railways[curLineID].LineLength);
               //前方有两股道时，开始计算距离，20米以后才显示；
               if(showmap_stat && ((curStation.Railways[preLineID].nextLineN> 1 && trainonlinedirect == 1) ||
                                   (curStation.Railways[preLineID].preLineN > 1 && trainonlinedirect == 2)))
               {
                   showmap_stat = false;
                   trainSP=curTrainPos;
               }
           }
           if (cur_foot_index < 0)  continue;
       }
       else {//如果定位出的股道编号和当前股道编号一致，继续在轨计算
           preLineID = curLineID;
           curLineID = curStation.Railways[min_i].PlineID;
       }
        //以下计算机车BDS坐标用原坐标在轨道中心线的垂足坐标代替
        //计算机车距轨道起点的距离，单位米
        float per0 = find_disp_to_begin(&curStation,trainFoot, curLineID, cur_foot_index);
        //根据机车运行方向、车头位置和天线安装位置修正per0；
        float per = DistModify(per0,trainonlinedirect,&udp_dat.lspLKJ);

        PutLinePer(pre_LinePer,per);
        if(inlinedataid<3) inlinedataid = inlinedataid + 1;
        if(inlinedataid>1){ //确定机车在轨道上的运行方向
            Check_Per_Direct(train_line_direct,pre_LinePer);
            if (check_if_ok(train_line_direct)) {//机车运行切换方向 必须连续4次判断一致
                pre_trainonlinedirect = trainonlinedirect;
                trainonlinedirect = train_line_direct[0];
            }
        }
        //确定机车前方进路和防控项点
        FindtheWayandPoint(&curStation,per, curLineID,trainonlinedirect,&KeyPointS,&KeyLineS);

        DCHEGouardInfoHead dchead84;
        dchead84.MSGID = udp_dat.lspBDS.MSGID;
        dchead84.ServerId = what_time_is_it_sec(); 
        dchead84.TLJ = udp_dat.TLJ-1;
        dchead84.StationID = udp_dat.stationID-1;
        dchead84.TDCSStat = 0;
        dchead84.MonStat =  udp_dat.lspLKJ.EquipStatus;
        dchead84.Direct = udp_dat.lspLKJ.TrainDirect;

        byte bbuf[2048]={0};
        int  tlen=0;
        byte *p_cl_ad;//lsp socket 回传数据用
        p_cl_ad=(byte *)&udp_dat.client_addr;
        for(int i=0;i<16;i++){
            bbuf[tlen++]=p_cl_ad[i];
        }
        
        tlen = tlen + sizeof(DCHEGouardInfoHead);
        int startA=tlen;
        bbuf[tlen++]=0;                 //标志0：MA信息，
        bbuf[tlen++]=0;                 //数据MA长度
        bbuf[tlen++]=0;
        bbuf[tlen++]=0;                 //预留
        bbuf[tlen++]=0;                 //预留
        bbuf[tlen++]=KeyPointS.size();  //包数 防控项点数量
        for(int i=0;i<KeyPointS.size();i++){
            bbuf[tlen++]=KeyPointS[i].PointType;
            bbuf[tlen++]=KeyPointS[i].PointID;
            bbuf[tlen++]=KeyPointS[i].PointID >>8;
            for(int j=0;j<12;j++){
                bbuf[tlen+j]=KeyPointS[i].PointName[j]; //防控项点名称
            }
            tlen=tlen+12;
            bbuf[tlen++]=KeyPointS[i].PointV;     //防控项点状态
            bbuf[tlen++]=KeyPointS[i].PointV >> 8;
            bbuf[tlen++]=KeyPointS[i].PointV >> 16;
            bbuf[tlen++]=KeyPointS[i].PointV >> 24;

            bbuf[tlen++]=KeyPointS[i].PointDis;   //防控项点的距离（cm）      
            bbuf[tlen++]=KeyPointS[i].PointDis >> 8;
            bbuf[tlen++]=KeyPointS[i].PointDis >> 16;
            bbuf[tlen++]=KeyPointS[i].PointDis >> 24;

            bbuf[tlen++]=0;             //预留
            bbuf[tlen++]=0;             //预留
        }
        int salen= tlen-16-sizeof(DCHEGouardInfoHead);
        bbuf[startA+1]=salen;
        bbuf[startA+2]=salen >>8;               //数据A长度

        int startB=tlen;

        bbuf[tlen++]=1;                 //标志：1 前方股道/道岔
        bbuf[tlen++]=0;                 //数据长度
        bbuf[tlen++]=0;
        bbuf[tlen++]=0;                 //预留
        bbuf[tlen++]=0;                 //预留
        bbuf[tlen++]=KeyLineS.size();   //包数 股道/道岔数量
        
        for(int i =0;i<KeyLineS.size();i++)
        {
            bbuf[tlen++]=KeyLineS[i].RailwayID;    //股道/道岔1的编号
            bbuf[tlen++]=KeyLineS[i].RailwayID >>8; 
            bbuf[tlen++]=0;             //预留
            bbuf[tlen++]=0;             //预留
            bbuf[tlen++]=0;             //预留
            bbuf[tlen++]=0;             //预留
            for(int j=0;j<30;j++){      //股道/道岔1的名称
                bbuf[tlen+j]=KeyLineS[i].RailwayName[j];
            }
            tlen=tlen+30;                     
            bbuf[tlen++]=KeyLineS[i].DisToDC;   //股道/道岔的距离（cm）
            bbuf[tlen++]=KeyLineS[i].DisToDC >> 8;
            bbuf[tlen++]=KeyLineS[i].DisToDC >> 16;
            bbuf[tlen++]=KeyLineS[i].DisToDC >> 24;
            bbuf[tlen++]=KeyLineS[i].RailLength;   //股道/道岔的长度（cm）
            bbuf[tlen++]=KeyLineS[i].RailLength >> 8;
            bbuf[tlen++]=KeyLineS[i].RailLength >> 16;
            bbuf[tlen++]=KeyLineS[i].RailLength >> 24;
            bbuf[tlen++]=KeyLineS[i].RailwayStat;  //股道/道岔的状态值 
            bbuf[tlen++]=KeyLineS[i].RailwayStat >> 8;
            bbuf[tlen++]=KeyLineS[i].MaxSpeed;     //股道/道岔的限速值 
            bbuf[tlen++]=KeyLineS[i].MaxSpeed >> 8;
            bbuf[tlen++]=KeyLineS[i].direct;       //表示机车在此股道上的运行方向 
                                                   //  1 起点->终点  0 终点->起点 其它不确定
        }      
        int sblen=tlen-startB;   
        bbuf[startB+1] =sblen;         //数据长度
        bbuf[startB+2] =sblen >> 8;    

        dchead84.Length =  tlen-16; 
        memcpy(&bbuf[16],&dchead84,sizeof(DCHEGouardInfoHead));
       //根据股道自动化信息生成0x83进路报文
       if(gd_index>=0){
            DCHERouteInfoHead dchead83;
            dchead83.Version = 10;
            dchead83.TLJ = udp_dat.TLJ-1;
            dchead83.StationID = udp_dat.stationID-1;
            time_t rawtime;
            struct tm * timeinfo;
            dchead83.Year=timeinfo->tm_year + 1900;
            dchead83.Month= timeinfo->tm_mon + 1;
            dchead83.Day = timeinfo->tm_mday;
            dchead83.Hour =timeinfo->tm_hour;
            dchead83.Min = timeinfo->tm_min;
            dchead83.Sec = timeinfo->tm_sec;

            G_V.Mutex_gdzdh[netid][gd_index].lock();
            dchead83.Length =sizeof(DCHERouteInfoHead)+ G_V.jwdBuf[netid][gd_index].bufLen;
            memcpy(&bbuf[tlen],&dchead83,sizeof(DCHERouteInfoHead));              
            tlen=tlen+sizeof(DCHERouteInfoHead);
            memcpy(&bbuf[tlen],G_V.jwdBuf[netid][gd_index].jwdbuf,G_V.jwdBuf[netid][gd_index].bufLen);
            tlen=tlen+G_V.jwdBuf[netid][gd_index].bufLen;
            G_V.Mutex_gdzdh[netid][gd_index].unlock();
       } 


        SendBuftoCMS(bbuf,tlen,1,frameN++);

        printf("frame: %d\n",frameN);
        printf("Line: ");
        for(int i=0;i<KeyLineS.size();i++)
        {
            printf("%d, ",KeyLineS[i].RailwayID);
        }
        printf("\n");
        printf("Thread=%d Per0=%6.1f Per1=%6.1f direct= %d\n",q_id,pre_LinePer[0],pre_LinePer[1],
                                                    trainonlinedirect);

        DBSaveInfo db_info;
        GetDBSInfoFromLSP(&db_info, &udp_dat); 
        if(KeyPointS.size()>0){
            db_info.SignalType=KeyPointS[0].PointType;
            db_info.SignalID=KeyPointS[0].PointID;
            db_info.SignalDis=KeyPointS[0].PointDis;
            db_info.SignalV=KeyPointS[0].PointV;
            strcpy(db_info.SignalName,KeyPointS[0].PointName);
        }else{
            db_info.SignalType=0;
        }
        db_info.RailwayID = KeyLineS[0].RailwayID;
        db_info.RailwayStat = KeyLineS[0].RailwayStat;
        strcpy(db_info.RailwayName,KeyLineS[0].RailwayName);
        db_info.RailLength = KeyLineS[0].RailLength;
        db_info.MaxSpeed = KeyLineS[0].MaxSpeed; 
        db_info.DisToDC = KeyLineS[0].DisToDC;
        db_info.StationID = pre_StationID;
        db_info.LineDirect = trainonlinedirect; 
        db_info.LinePer = per / curStation.Railways[curLineID].LineLength;
        char savebuf[2048]={0};
        int slen=sizeof(db_info);
        memcpy(savebuf,&db_info,slen);

        savebuf[slen++]=KeyPointS.size();  //防控项点数量
        for(int i=0;i<KeyPointS.size();i++)
        {
            savebuf[slen++]=KeyPointS[i].PointType;
            savebuf[slen++]=KeyPointS[i].PointID;
            savebuf[slen++]=KeyPointS[i].PointID >>8;
            savebuf[slen++]=strlen(KeyPointS[i].PointName);
            for(int j=0;j<strlen(KeyPointS[i].PointName);j++){
                savebuf[slen++]=KeyPointS[i].PointName[j]; //防控项点名称
            }
            savebuf[slen++]=KeyPointS[i].PointV;     //防控项点状态
            savebuf[slen++]=KeyPointS[i].PointV >> 8;
            savebuf[slen++]=KeyPointS[i].PointV >> 16;
            savebuf[slen++]=KeyPointS[i].PointV >> 24;

            savebuf[slen++]=KeyPointS[i].PointDis;   //防控项点的距离（cm）      
            savebuf[slen++]=KeyPointS[i].PointDis >> 8;
            savebuf[slen++]=KeyPointS[i].PointDis >> 16;
            savebuf[slen++]=KeyPointS[i].PointDis >> 24;
        }

        savebuf[slen++]=KeyLineS.size();   //股道/道岔数量
        for(int i =0;i<KeyLineS.size();i++)
        {
            savebuf[slen++]=KeyLineS[i].RailwayID;    //股道/道岔1的编号
            savebuf[slen++]=KeyLineS[i].RailwayID >>8; 
            savebuf[slen++]=strlen(KeyLineS[i].RailwayName);             
            for(int j=0;j<strlen(KeyLineS[i].RailwayName);j++){      //股道/道岔1的名称
                savebuf[slen++]=KeyLineS[i].RailwayName[j];
            }
            savebuf[slen++]=KeyLineS[i].DisToDC;   //股道/道岔的距离（cm）
            savebuf[slen++]=KeyLineS[i].DisToDC >> 8;
            savebuf[slen++]=KeyLineS[i].DisToDC >> 16;
            savebuf[slen++]=KeyLineS[i].DisToDC >> 24;
            savebuf[slen++]=KeyLineS[i].RailLength;   //股道/道岔的长度（cm）
            savebuf[slen++]=KeyLineS[i].RailLength >> 8;
            savebuf[slen++]=KeyLineS[i].RailLength >> 16;
            savebuf[slen++]=KeyLineS[i].RailLength >> 24;
            savebuf[slen++]=KeyLineS[i].RailwayStat;  //股道/道岔的状态值 
            savebuf[slen++]=KeyLineS[i].RailwayStat >> 8;
            savebuf[slen++]=KeyLineS[i].MaxSpeed;     //股道/道岔的限速值 
            savebuf[slen++]=KeyLineS[i].MaxSpeed >> 8;
            savebuf[slen++]=KeyLineS[i].direct;       //表示机车在此股道上的运行方向 
                                                   //  1 起点->终点  0 终点->起点 其它不确定
        }   
        for(int i=0;i<slen;i++){                   //校验和 
            savebuf[slen]=savebuf[slen]+savebuf[i];
        }
        //双通道数据发送给数据库服务器
        for(int i=0;i<2;i++){
            sendto(socket_send,savebuf,slen+1,0,(struct sockaddr *)&DBSAddr[i],sizeof(DBSAddr[i]));  
        }
        usleep(10000);
   }
   G_V.lspQue[q_id].queMutex.unlock();
   G_V.lspQue[q_id].queStat=0;
   //清除线程标志
   string trainstr;
   trainstr.assign(udp_dat.lspBase.TrainNumber, udp_dat.lspBase.TrainNumber + 8);
   auto iter=lsp_threads.find(trainstr);

   //auto iter=lsp_threads.find(udp_dat.lspBase.TrainNumber);
   if (iter!= lsp_threads.end()) {
        lspThreadMux.lock();
        lsp_threads.erase(iter);
        lspThreadMux.unlock();
    }                                
   
   char tempbuf[128];
   sprintf(tempbuf,"针对机车%d-%d的计算线程退出.",TypeID,TrainN);
   rk_syslog(tempbuf);            
   
 }