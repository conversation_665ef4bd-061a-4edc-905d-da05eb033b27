#ifndef DS_DATA_H
#define DS_DATA_H

#include <stdio.h>
#include <string.h>
#include <time.h>
#include <netinet/in.h>
#include <queue>
#include <vector>
#include <mutex>

#pragma pack(push)
#pragma pack(1)
typedef unsigned char byte;

using namespace std;
typedef struct _ForwBuf
{
  u_int16_t len;
  byte buf[4096];  
  _ForwBuf(){
        memset(this,0,sizeof(_ForwBuf));    
        this->buf[0]=0xFA;
        this->buf[1]=0xFA;
  }
}ForwBuf;

typedef struct _BASEMSG   //基础信息包 0x01
{
	byte           DataType;		//0x01
	uint16_t         Length;		//信息包长度
	byte          FactoryID;		//厂家编号
	uint32_t      TimeStamp;	    //发送数据时的时间戳(s)
	char     TrainNumber[8];	    //列车编号
	char             Status;		//0：本务机，1：轨道车
	byte       IDentity[32];		//身份ID（CPU-UID）
	char                TLJ;		//所属局码
	uint16_t      Sectionno;	 	//所属段码
	uint32_t          Space;	  	//预留

	_BASEMSG(){
		memset(this,0,sizeof(_BASEMSG));

		this->DataType = 0x01;
		this->Length = sizeof(_BASEMSG); 
        this->FactoryID = 65;
	}
}BASEMSG;

typedef struct _BDSMSG  //北斗解析信息包 0x02
{
	byte          DataType;			   //0x02
	uint16_t        Length;			   //信息包长度
    byte             Space1;		   //预留（信号机切换）
	int          Longitude;			   //经度（度数*10000000）
	int           Latitude;			   //纬度（度数*10000000）
	short            Speed;			   //精确到公里的速度
    uint64_t         MSGID;			   //消息包ID  主机电脑时间毫秒数
	byte              Flag;			   //原始定位标志
	uint16_t      BaseStID;		       //使用差分基站
	uint16_t    BaseStTime;	           //使用差分基站时效
	char           StarNum;			   //卫星数
    uint32_t         Space2;           //预留

	_BDSMSG(){
		memset(this,0,sizeof(_BDSMSG));
		this->DataType = 0x02;
		this->Length = sizeof(_BDSMSG);	
	}

}BDSMSG;

typedef struct _TAXMSG  //机车状态信息包 基于tax箱产生 0x03
{
	byte         DataType;			  //0x03
	uint16_t       Length;			  //信息包长度
	uint16_t    MAOuttime;			  //MA时效性
	char     TrainOuttime;		      //车载数据时效性
	char         IsCanUse;			  //LKJ数据是否可用1：可用，其他不可用
	uint16_t        Speed;			  //机车速度
	char      TrainDirect;			  //车头方向，1向前，16向后，
	char   TrainCondition;		      //对应位为“1”表示有效 D3:牵引 D2:向前 D1向后 D0:零位 (机车工况)
	char      EquipStatus;			  //D7:通常模式 D6:调车模式  D4:降级模式 D2:非本务模式 (监控状态状态）
	char             KHBB;
	char   TrainNumber[8];		      //4 车次字母部分 4 车次数字部分
	char      ErrorNumber;		      //b1b0: 1与TAX通讯是否故障 b3b2: 1BDS故障 
								      //b5b4: 1剩余MA报警，未越信号机；2剩余MA报警，越过信号机
	uint32_t     UseMSGID;			  //当前使用的服务器MSGid
	int          Distance;			  //运行距离
	short       DistanceA;		      //天线距离I端长度 cm
	short       DistanceB;		      //天线距离II端长度 cm
	
	short    TrainSignDis;            //车载主机计算距离前方信号机距离
	short   TrainCheckDis;		  	  //车载主机计算矫正距离
	char    TrainOwnCheck;	          //工况自检
	char    DriverNotrain;		      //司机确认前方无车/推车
								                //0：未确认
								                //1:20s内司机确认无车
								                //2:20s内司机确认推车
	char         space[4];			  //预留
	                                  //暂定space[0]用于记录事件类型 

	_TAXMSG(){
		memset(this,0,sizeof(_TAXMSG));
		this->DataType = 0x03;
		this->Length = sizeof(_TAXMSG);
	}

}TAXMSG;

typedef struct _LSPDat
{
   struct sockaddr_in client_addr;//LSP Socket地址
   BASEMSG  lspBase;  //基础信息包
   BDSMSG   lspBDS;   //北斗解析信息包
   TAXMSG   lspLKJ;   //机车状态信息包
   int      stationID;
   int      TLJ;
}LSPDat;

typedef struct _BWZJHeart           //心跳包
{
    byte         DataType;		    //0x0C
    uint16_t       Length;		    //信息包长度
    byte            MsgID;          //消息ID，从0开始计数
    byte          YuLu[4];
    uint16_t         Year;          //年
    byte            Month;          //月
    byte              Day;          //日
    byte             Hour;          //时
    byte              Min;          //分
    byte              Sec;          //秒
    byte              crc;

    _BWZJHeart(){
        memset(this,0,sizeof(_BWZJHeart));
        this->DataType=0x0C;
        this->Length = sizeof(_BWZJHeart);
    }
}BWZJHeart;

typedef struct _PPoint
{
    char       PointType;           //防控项点类型
    uint16_t     PointID;           //防控项点编号
    char   PointName[12];           //防控项点名称
    uint32_t      PointV;           //防控项点状态
    uint32_t    PointDis;           //防控项点距离
    _PPoint(){
        memset(this,0,sizeof(_PPoint));
    }
}PPoint;

typedef struct _DCHERouteInfoHead   //调车进路信息包包头
{
	byte         DataType;			//0x83
	uint16_t       Length;			//信息包长度
	int           Version;          //数据版本
    uint16_t         Year;          //服务器时间
    byte            Month;
    byte              Day;
    byte             Hour;
    byte              Min;
    byte              Sec;
	char              TLJ;          //局码
    uint16_t    StationID;          //站码
    char         Space[4];  	    //预留
    _DCHERouteInfoHead(){
		memset(this,0,sizeof(_DCHERouteInfoHead));
		this->DataType = 0x83;
	}	
}DCHERouteInfoHead;

typedef struct _DCHEGouardInfoHead   //调车防护信息包包头
{
    byte         DataType;			//0x84
	uint16_t       Length;			//信息包长度
    uint64_t        MSGID;          //对应接受车载发送包中消息包ID
	int          ServerId;          //服务器ID
	char  PushTrainmsg[4];          //B1，B2：预留    B3：是否推车 (0:否，1：推车)  B4：space
	char              TLJ;          //局码 
    uint16_t    StationID;          //站码
    uint16_t      Version; 	        //版本
	uint16_t  VersionNext; 	        //下一站版本
    char         Space[3];  	    //预留
	char         TDCSStat;          //站场是否有TDCS 1：有TDCS站 2：无TDCS站  其他：未知
	char          MonStat;          //监控模式, 回填车载03包中监控状态，用于判断非调车、非调防模式下不使用该MA信息 
	char       RoutePrecs;          //进路精准度,  1：通过北斗精准计算,  2：距离推算  其他：未知 
	char               xb;          //上下行（0：上行，1：下行，其他不确定）前方第一架信号机属性判断
	char           Direct;          //进路方向（1向前，16向后，其他不确定）回填车载方向	
    
	
    _DCHEGouardInfoHead(){
		memset(this,0,sizeof(_DCHEGouardInfoHead));
		this->DataType = 0x84;
		this->Version  =1;
	}	
}DCHEGouardInfoHead;


typedef struct _TRAINPOSInfo   //机车位置信息包 0x85
{
	byte         DataType;			//0x85
	uint16_t       Length;			//信息包长度
    uint64_t        MSGID;			//对应接受车载发送包中消息包ID
	int          ServerId;          //服务器ID
    char         MaStatus;          //服务器情况
	char         Space1[3];         //预留
	char              TLJ;          //局码
	uint16_t    StationID;          //站码
	uint32_t      Version;          //版本
	char       SignalType;          //前方防控项点类型
    uint16_t     SignalID;          //前方防控项点编号	
	uint16_t    SignalDis;          //前方防控项点距离	
	char           Space2;          //预留
	char               xb;          //上下行（0：上行，1：下行，其他不确定）前方第一架信号机属性判断
	char           Direct;          //进路方向（1向前，16向后，其他不确定）回填车载方向
	char            FlagB;          //标志：1 前方股道/道岔
	uint16_t     DataBLen;          //数据B长度
	int          CheckLen;          //服务器回填车载走行，车载校正
	char        Space3[4];          //预留
	char             NumB;          //包数 1有位置 ，0做心跳
	uint16_t    RailwayID;          //股道/道岔的编号
	char        Space4[4];          //预留
	char  RailwayName[30];          //股道/道岔的名称
	uint32_t      DisToDC;          //股道/道岔的距离（cm）         
	uint32_t   RailLength;          //股道/道岔的长度（cm）         
	uint16_t  RailwayStat;          //股道/道岔的状态值
                                    //用多个比特位的值描述状态时，值按高位在前、低位在后的原则计算得出。	
                                    //16bit（自定义），1表示机车运行方向 起点->终点  0 终点->起点
	uint16_t     MaxSpeed;			//股道/道岔的限速值					 
    _TRAINPOSInfo(){
        memset(this,0,sizeof(_TRAINPOSInfo));
        this->Length = sizeof(_TRAINPOSInfo);
		this->DataType = 0x85;
	}
}TRAINPOSInfo;

typedef struct _DBSaveInfo   //数据库记录信息包 0x55CC
{
	unsigned short        header;          //头信息
    uint16_t              TypeID;          //机车类型ID   
    uint32_t              TrainN;          //机车编号
	byte               FactoryID;		   //厂家编号
	char                     TLJ;		   //所属局码
    uint16_t                 JWD;	 	   //所属段码
	char            DateTime[20];          //"xxxx-xx-xx xx:xx:xx"    
	int                Longitude;		   //经度（度数*10000000）
	int                 Latitude;		   //纬度（度数*10000000）
	uint16_t           StationID;          //站码
	uint16_t               Speed;		   //机车速度
	char             TrainDirect;		   //车载方向，1向前，16向后，
	char          TrainCondition;		   //对应位为“1”表示有效 D3:牵引 D2:向前 D1向后 D0:零位 (机车工况)
	char             EquipStatus;		   //D7:通常模式 D6:调车模式  D4:降级模式 D2:非本务模式 (监控状态状态）
	char                    KHBB;
	char          TrainNumber[8];		   //4 车次字母部分 4 车次数字部分
	char             ErrorNumber;		   //b1b0: 1与TAX通讯是否故障 b3b2: 1BDS故障 
								           //b5b4: 1剩余MA报警，未越信号机；2剩余MA报警，越过信号机	 
	char              SignalType;          //前方防控项点类型
    uint16_t            SignalID;          //前方防控项点编号	
	uint16_t           SignalDis;          //前方防控项点距离											   
	char          SignalName[20];          //前方防控项点名称
	uint32_t             SignalV;          //前方防控项点状态
	uint16_t           RailwayID;          //当前股道/道岔的编号
	char         RailwayName[20];          //当前股道/道岔的名称
	uint32_t             DisToDC;          //当前股道/道岔的距离（cm）         
	uint32_t          RailLength;          //当前股道/道岔的长度（cm）         
	uint16_t         RailwayStat;          //当前股道/道岔的状态值
	float                LinePer;          //当前线路百分比位置
	char              LineDirect;          //当前线路方向 1 面向起点  2 面向终点  0 未知
                                           //用多个比特位的值描述状态时，值按高位在前、低位在后的原则计算得出。	
                                           //16bit（自定义），1表示机车运行方向 起点->终点  0 终点->起点
	uint16_t            MaxSpeed;          //股道/道岔的限速值					 
	char               EventType;          //调车过程发生的事件
    _DBSaveInfo()
    {
        this->header = 0x55CC;
	} 
}DBSaveInfo;


#pragma pack(pop)

typedef struct _DSPoint
{
    float x;        //X 坐标
    float y;        //Y 坐标
    _DSPoint() {
        x = 0;
        y = 0;
    }
}DSPoint;

typedef struct _DPPoint
{
    float x;        //X 坐标
    float y;        //Y 坐标
    _DPPoint() {
        x = 0;
        y = 0;
    }
}DPPoint;

typedef struct _BWStation
{
    byte                      TLJ;       //局码
    uint16_t            StationID;       //站码
    char             TLJ_Name[20];       //铁路局
    char         Station_Name[30];       //站场名称
    uint16_t           pyPoints_N;       //站场多边形范围点数
    std::vector<float>      vertx;       //站场多边形范围边界点容器(经度)
	std::vector<float>      verty;       //站场多边形范围边界点容器(纬度)
//    _BWStation(){
//         memset(this,0,sizeof(_BWStation));
//    }
}BWStation;

typedef struct _Railway
{
	uint16_t    RailwayID;          //股道/道岔的编号
	char  RailwayName[30];          //股道/道岔的名称
	uint32_t      DisToDC;          //股道/道岔的距离（cm）         
	uint32_t   RailLength;          //股道/道岔的长度（cm）         
	uint16_t  RailwayStat;          //股道/道岔的状态值
	uint16_t     MaxSpeed;			//股道/道岔的限速值		
	char           direct;          //表示机车在此股道上的运行方向   1 起点->终点  0 终点->起点 其它不确定
	char               xb;          //上下行（0：上行，1：下行，其他不确定）
//	_Railway(){
//		memset(this,0,sizeof(_Railway));
//	}
}Railway;



typedef struct _FKPoint
{
    char       PointType;           //防控项点类型    // 1 信号灯  2 接触网终点标  3 脱轨器  4 土挡
                                                    // 5 站界    6 道岔     7 无网线路 8 特殊防控项点 9 禁停区
    uint16_t     PointID;           //防控项点编号
    char   PointName[12];           //防控项点名称
    uint32_t      PointV;           //防控项点状态
    uint32_t    PointDis;           //防控项点距离
//    _FKPoint(){
//        memset(this,0,sizeof(_FKPoint));
//    }
}FKPoint;


typedef struct _LSPDatQue  
{
  int queStat;	           //0：空闲 1：有数据
  mutex queMutex;  
  queue<LSPDat>cmsdatQue;  //接受的车载数据队列
  _LSPDatQue(){
        this->queStat=0;
  }
}LSPDatQue;

typedef struct _LSPThread
{
  pthread_t  pid;
  int        que_index;         //队列序号 
}LSPThread;

//股道自动化信息
typedef struct _gdzdhBuf
{
    unsigned short JWD_ID;
    int bufLen;
    time_t timestamp;
    char jwdbuf[51200];
}gdzdhBuf;


//全局变量
typedef struct _StaticVar
{
    char                       *cmd_path;        //执行程序所在目录
    mutex               mtxudptoCMSQueue;        //地算业务数据容器对应锁
    queue<ForwBuf>         udptoCMSQueue;        //地算业务数据容器
    char                  LocalIP[2][20];        //地算服务器内网IP
    char                    CMSIP[2][20];        //通信服务器内网IP
	int                          CMSPort;        //通信服务器端口
    bool                     CMS_Stat[2];        //通信服务器心跳包状态

    char                     DBIP[2][20];        //数据库服务器内网IP
	bool                     DBS_Stat[2];        //数据库服务器心跳包状态
	int                           DBPort;        //数据库服务器UDP端口
	struct sockaddr_in         DBAddr[2];        //数据库Socket地址 

    mutex              mtxudpfrCMSQue[2];        //通信服务器UDP数据容器对应锁
    queue<ForwBuf>        udpfrCMSQue[2];        //通信服务器UDP数据容器
    byte                 TrdNforUDPfrCMS;        //向通信服务器转发udp数据的线程数量
	vector<gdzdhBuf>           jwdBuf[2];        //股道自动化信息    双通道
    mutex            Mutex_gdzdh[2][500];        //股道自动化信息对应锁

	LSPDatQue               lspQue[5000];        //本务机车数据队列，一车一个；
	char                   Map_Path[100];        //地图文件路径

	mutex                        IDX_Mux;
    vector<BWStation> ltdw_stations;        //蓝天测绘站场容器
    uint16_t                       IDX_V;        //站场索引文件版本

}StaticVar;

#endif //DS_DATA_H
