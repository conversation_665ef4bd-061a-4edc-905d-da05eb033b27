//udp通讯模块
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <time.h>
#include <fcntl.h>
#include <unistd.h>
#include <arpa/inet.h>
#include <sys/socket.h>
#include <sys/time.h>
#include <netinet/in.h>
#include <iconv.h>
#include <map>
#include <mutex>

#include "zlib.h"
#include "DS_Data.h"
#include "crc.h"
#include "tx2_filelog.h"

using namespace std;

struct sockaddr_in ServerAddr;

uint16_t FreamNum = 0, DS_Id[2]={0};
uint16_t pre_DS_Id[2], cur_DS_Id[2];

uint16_t  DB_Id[2]={0};
uint16_t pre_DB_Id[2], cur_DB_Id[2];



mutex lspThreadMux;
map<string, LSPThread> lsp_threads;//地算线程容器

map<string, LSPThread>::iterator iter;  

extern StaticVar G_V;

void PushMapBuf(struct sockaddr_in client_addr,ForwBuf fBuf);

void *DS_Cal_For_lsp(void *lpPara);//地算线程，每车1个


/// 检查，加载站场图索引
void LoadStationIdx()
{   
   char filename[512];
   sprintf(filename,"%s/station.idx",G_V.Map_Path);
   FILE* fp;
   long long cur_index = 0;
   fp = fopen(filename, "rb");
   fseek(fp, 0, SEEK_END);//移到文件尾
   long long fileSize = ftell(fp);
   fseek(fp, 0, SEEK_SET);//移到文件头
   byte* fbuf = (byte*)malloc(fileSize);//文件字节数
   fread(fbuf, 1, fileSize, fp);
   uint32_t crc = CRC32(fbuf, fileSize-4);
   uint32_t* oldcrc = (uint32_t*) (&fbuf[fileSize - 4]);
   if(crc != *oldcrc){
      rk_syslog("文件station.idx CRC 错误.");
      fclose(fp);
      return;
   }
   fclose(fp);
   unsigned char BigV,SmallV;
   BigV=fbuf[0];
   SmallV=fbuf[1];
   if(BigV*256+SmallV<=G_V.IDX_V) return;//版本低返回
   G_V.IDX_V=BigV*256+SmallV;

   G_V.IDX_Mux.lock();
   G_V.ltdw_stations.clear();
   uint16_t totalN=*(uint16_t *)&fbuf[9];
   cur_index=15;
   for(int i=0;i<totalN;i++){
       BWStation tmpStation;
       tmpStation.TLJ=fbuf[cur_index++];
       unsigned char nl=fbuf[cur_index++];
       for(int j=0;j<nl;j++){
         tmpStation.TLJ_Name[j]=fbuf[cur_index++];
       }
       tmpStation.TLJ_Name[nl]='\0';
       tmpStation.StationID=*(uint16_t *)&fbuf[cur_index];
       cur_index=cur_index+2;
       nl=fbuf[cur_index++];
       for(int j=0;j<nl;j++){
         tmpStation.Station_Name[j]=fbuf[cur_index++];
       }
       tmpStation.Station_Name[nl]='\0';
       tmpStation.pyPoints_N=*(uint16_t *)&fbuf[cur_index];
       cur_index=cur_index+2;
       
       for(int j=0;j<tmpStation.pyPoints_N;j++){
          int xx=*(int *)&fbuf[cur_index]; 
          float mtx= xx/10000000.0;
          cur_index=cur_index+4;
          int yy =*(int *)&fbuf[cur_index];
          float mty=yy/10000000.0;
          cur_index=cur_index+4;
          tmpStation.vertx.push_back(mtx);
          tmpStation.verty.push_back(mty);
       }
       G_V.ltdw_stations.push_back(tmpStation);
   }
   G_V.IDX_Mux.unlock();
}

///周期性检查索引文件是否需要加载
void *Thead_LoadIDX(void *lpPara)
{
    G_V.IDX_V=0;
    while(1){
      LoadStationIdx();
      sleep(3600);
    }
}

long what_time_is_it_now()
{
    long res;
    struct timeval time;
    if (gettimeofday(&time,NULL)){
        return 0;
    }
    res = time.tv_sec*1000 + time.tv_usec*0.001;
    return res;
}

int what_time_is_it_sec()
{
    int res;
    struct timeval time;
    if (gettimeofday(&time,NULL)){
        return 0;
    }
    res = time.tv_sec;
    return res;
}

int DataDLEDealFun(unsigned char* Dst,unsigned char* Src,int len,char flag)
{
        if (len < 2)
        {
                return -1;
        }

        int pos = 0;

        if (flag == 0)
        {
                for (int i = 0;i < len;i++)
                {
                        if (Src[i] == 0x10 && i > 1 && i< len -2)
                        {
                                Dst[pos] = 0x10;
                                pos ++;
                        }
                        Dst[pos] = Src[i];
                        pos ++;
                }
        }
        else if (flag == 1)
        {
                for (int i = 0;i < len;i++)
                {
                        if (Src[i] == 0x10 && i > 1 && i< len -2)
                        {
                                i++;
                        }
                        Dst[pos] = Src[i];
                        pos ++;
                }
        }
        return pos;
}

int GetQueIndex()
{
    int i=0;
    for(i=0;i<5000;i++){
        if(G_V.lspQue[i].queStat==0) 
        {
            G_V.lspQue[i].queStat=1;
            return i;
        }
        
    }
    return -1;
}

//向通信服务发送地算服务器业务包, 双通道发送UDP数据
void* Thead_ForwardUDP(void *lpPara)
{
    struct sockaddr_in CMSAddr[2];
    int    socket_send;
    socket_send=socket(AF_INET,SOCK_DGRAM,0); //USE UDP
    for(int i=0;i<2;i++){
        memset(&CMSAddr[i], 0, sizeof(sockaddr_in));
        CMSAddr[i].sin_family = AF_INET;
        CMSAddr[i].sin_port = htons(10001);
        CMSAddr[i].sin_addr.s_addr = inet_addr(G_V.CMSIP[i]);
    }
    ForwBuf tempObj;
    while(1){
      G_V.mtxudptoCMSQueue.lock();
      if(!G_V.udptoCMSQueue.empty()){
         tempObj = G_V.udptoCMSQueue.front();
         G_V.udptoCMSQueue.pop();
         G_V.mtxudptoCMSQueue.unlock();
         for(int i=0;i<2;i++){
            sendto(socket_send,(char*)tempObj.buf,tempObj.len,0,(struct sockaddr *)&CMSAddr[i],sizeof(sockaddr_in));
         }
      }else
      {
         G_V.mtxudptoCMSQueue.unlock();
      }
    }
}


//接收通信服务器业务包,压入队列
void* Thread_RecDatafromDSS(void *lpPara)
{
    byte index;
    index=*(byte *)lpPara;
    int DSSoc = 0;
    if ((DSSoc = socket(AF_INET, SOCK_DGRAM, 0)) < 0)
    {
        return ((void*)0);
    }
    struct sockaddr_in LocAddr;
    memset(&LocAddr, 0, sizeof(LocAddr));
    LocAddr.sin_family = AF_INET;
    LocAddr.sin_port = htons(10001);
    LocAddr.sin_addr.s_addr = inet_addr(G_V.LocalIP[index]);
    //LocAddr.sin_addr.s_addr = INADDR_ANY;
    if(bind(DSSoc, (struct sockaddr *)&LocAddr, sizeof(sockaddr)) < 0){
        return ((void*)0);
    }
    unsigned char RecBuf[5120] = "";
    while(1){
       int res = recv(DSSoc,RecBuf,sizeof(RecBuf),0);
       if(RecBuf[0]==0xFB && RecBuf[1]==0xFB)
       {
            ForwBuf tempBuf;
            tempBuf.len = res;
            for(int i=0;i<res;i++){
                tempBuf.buf[i]=RecBuf[i];
            }
            G_V.mtxudpfrCMSQue[index].lock();
            G_V.udpfrCMSQue[index].push(tempBuf);
            G_V.mtxudpfrCMSQue[index].unlock();
       }
       usleep(10);
   }
}

//确定点是否在多边形内部

bool pnpoly(int nvert, float* vertx, float* verty, float testx, float testy)
{
	int i, j;
	bool c = false;
	for (i = 0, j = nvert - 1; i < nvert; j = i++) {
		if (((verty[i] > testy) != (verty[j] > testy))  &&
			(testx < (vertx[j] - vertx[i]) * (testy - verty[i]) / (verty[j] - verty[i]) + vertx[i]))
			c = !c;
	}
	return c;
}



//获取站场ID，-1表示不在站场内
int GetStatID(float longitude,float latitude,int *TLJ)
{
   int stat=-1;
   int stationSize=G_V.ltdw_stations.size();
   for(int i=0;i<stationSize;i++)
   {
     int nsize=G_V.ltdw_stations[i].vertx.size();
     if(pnpoly(nsize,  &G_V.ltdw_stations[i].vertx[0],&G_V.ltdw_stations[i].verty[0],longitude,latitude)){
             stat=G_V.ltdw_stations[i].StationID;
             *TLJ=G_V.ltdw_stations[i].TLJ;
     }
   }
   //int stat=7;
   //*TLJ=1;
   return stat;
}

//处理通信服务数据队列数据
int ProcessCMSQueData(ForwBuf tp)
{
   int stat =0;
   LSPDat temp_lspdat;
   struct sockaddr_in client_addr;
   int socketsize= sizeof(sockaddr_in);
   
   temp_lspdat.client_addr=*(struct sockaddr_in*)&tp.buf[2]; //机车端 socket

   if(tp.buf[0] != 0xFB || tp.buf[1] != 0xFB)  return -1;
   uint16_t len =*(uint16_t *)&tp.buf[20];
   if(len != tp.len-18) return -1;
   uint32_t crc = CRC32(&tp.buf[18],len-4);
   uint32_t ocrc = tp.buf[tp.len-4] | tp.buf[tp.len-3] << 8 | tp.buf[tp.len-2] << 16 | tp.buf[tp.len-1] <<24;
   if(crc != ocrc)  return -2;
   if(tp.buf[18] != 0x5A || tp.buf[19] != 0x5A) return -3;//不是业务数据
   int pos = 18+8;
   int tstat=0;
   while(pos < tp.len - 4)//分解数据
   {
      int pLen = tp.buf[pos + 1] | tp.buf[pos + 2]<<8;
      if (pLen == 0)  break;
      tstat=0;
      if(tp.buf[pos]==0x01)           //基础信息包
      {
          temp_lspdat.lspBase=*(BASEMSG *)&tp.buf[pos];
          tstat=1;
      }else if(tp.buf[pos]==0x02)     //北斗解析信息包
      {
          temp_lspdat.lspBDS=*(BDSMSG *)&tp.buf[pos];
          tstat=1;
      }else if(tp.buf[pos]==0x03)     //机车状态信息包
      {
          temp_lspdat.lspLKJ=*(TAXMSG *)&tp.buf[pos];
          tstat=1;
      }else if(tp.buf[pos]==0x17)     //站场示意图更新-数据请求包;
      {
          PushMapBuf(temp_lspdat.client_addr,tp);
      }else if(tp.buf[pos]==0x18)     //站场示意图更新-版本确认包;
      {
          PushMapBuf(temp_lspdat.client_addr,tp);
      }else if(tp.buf[pos]==0x19)     //站场示意图更新-数据确认包
      {
          PushMapBuf(temp_lspdat.client_addr,tp);
      }

      pos += pLen;
    }

    if(tstat==0) return 0;
    
    //如果机车在站场内
    int stationID,TLJ;
    float tempx,tempy;
    tempx=temp_lspdat.lspBDS.Longitude/10000000.0;
    tempy=temp_lspdat.lspBDS.Latitude/10000000.0;
    stationID=GetStatID(tempx,tempy,&TLJ); 

    if(stationID>=0){
          temp_lspdat.stationID= stationID;
          temp_lspdat.TLJ =TLJ;
          string trainstr;
          trainstr.assign(temp_lspdat.lspBase.TrainNumber, temp_lspdat.lspBase.TrainNumber + 8);
          iter=lsp_threads.find(trainstr);
          if(iter != lsp_threads.end()) {//线程已经存在 压入数据
               int qindex=iter->second.que_index;
               G_V.lspQue[qindex].queMutex.lock();
               G_V.lspQue[qindex].cmsdatQue.push(temp_lspdat);
               G_V.lspQue[qindex].queMutex.unlock();
          }else{//线程不存在 
               LSPThread calthread;
               calthread.que_index=GetQueIndex();//获取一个没有被用的队列，产生线程
               pthread_create(&calthread.pid,NULL,DS_Cal_For_lsp,(void *)&calthread.que_index);

               lspThreadMux.lock();
               lsp_threads.insert(pair<string, LSPThread>(trainstr,calthread));  
               lspThreadMux.unlock();  
        
               G_V.lspQue[calthread.que_index].queMutex.lock();
               G_V.lspQue[calthread.que_index].cmsdatQue.push(temp_lspdat);
               G_V.lspQue[calthread.que_index].queMutex.unlock();
               uint16_t TypeID;
               uint32_t TrainN;
               TypeID=*(uint16_t *)&temp_lspdat.lspBase.TrainNumber[0];
               TrainN=*(uint32_t *)&temp_lspdat.lspBase.TrainNumber[2];
               char tempbuf[128];
               sprintf(tempbuf,"针对机车%d-%d的计算线程启动.",TypeID,TrainN);
               rk_syslog(tempbuf);                       
          } 
    }

   return stat;
   
 }


//处理通信服务数据队列
void *Thread_ProcessCMSQue(void *lpPara)
{
    byte index;
    index=*(byte *)lpPara;
    while(1){
        G_V.mtxudpfrCMSQue[index].lock();
        if(!G_V.udpfrCMSQue[index].empty()){
            ForwBuf tempBuf;
            tempBuf=G_V.udpfrCMSQue[index].front();
            G_V.udpfrCMSQue[index].pop();
            G_V.mtxudpfrCMSQue[index].unlock();
            if(index==0){//缺省以网络0数据为主
                if(G_V.CMS_Stat[index]){
                      int zt =  ProcessCMSQueData(tempBuf);
                      //printf("channel 0  %d\n",zt);
                }
            }else if(index==1){//网络0故障时转向网络1
                if(!G_V.CMS_Stat[0] && G_V.CMS_Stat[1]){
                      int zt =  ProcessCMSQueData(tempBuf);                    
                      //printf("channel 1  %d\n",zt);
                }
            }
        }else{
            G_V.mtxudpfrCMSQue[index].unlock();
        }
        usleep(10);
    }
}


//检查通信服务到地算服务器心跳包
void *Thread_CheckHeatBfromCMS(void *lpPara)
{
   sleep(5);
   while(1){
       for(int i=0;i<2;i++){
         pre_DS_Id[i]= cur_DS_Id[i];
         cur_DS_Id[i] = DS_Id[i];
         if(cur_DS_Id[i] != pre_DS_Id[i])
            G_V.CMS_Stat[i]=true;
         else{ 
            G_V.CMS_Stat[i]=false;   
            char tbuf[512];
            sprintf(tbuf,"通道%2d,没有收到通信服务心跳包.");
            rk_syslog(tbuf);
         }
       }
       sleep(2); 
   }
}

//接收通信服务器心跳包
void *Thread_RecHeatBfromCMS(void *lpPara)
{
    byte *index;
    index=(byte *)lpPara;

    int DSSoc = 0;
    if ((DSSoc = socket(AF_INET, SOCK_DGRAM, 0)) < 0)
    {
        return ((void*)0);
    }
    struct sockaddr_in LocAddr;
    memset(&LocAddr, 0, sizeof(LocAddr));
    LocAddr.sin_family = AF_INET;
    LocAddr.sin_port = htons(12302);
    LocAddr.sin_addr.s_addr = inet_addr(G_V.LocalIP[*index]);
    //LocAddr.sin_addr.s_addr = INADDR_ANY;
    if(bind(DSSoc, (struct sockaddr *)&LocAddr, sizeof(sockaddr)) < 0){
        return ((void*)0);
    }
    unsigned char RecBuf[512] = "";    
    while(1){
       int res = recv(DSSoc,RecBuf,sizeof(RecBuf),0);
       if(res>0 && RecBuf[0]==0x0C 
                && RecBuf[res-1] == 
                   AT1_checksc(res-1, (const unsigned char*)RecBuf) )
        {
          DS_Id[*index]=DS_Id[*index]+1;          
          //printf("%d :  %d\n",*index,DS_Id[*index]);
        }
        usleep(500000);
    }    
}


//地算服务器到通信服务心跳包
void* Thread_HeatBtoCMS(void *lpPara)
{
    struct sockaddr_in CMSAddr[2];
    int    socket_send;
    byte       MsgID=0;
    socket_send=socket(AF_INET,SOCK_DGRAM,0); //USE UDP
    for(int i=0;i<2;i++){
        memset(&CMSAddr[i], 0, sizeof(sockaddr_in));
        CMSAddr[i].sin_family = AF_INET;
        CMSAddr[i].sin_port = htons(12302);
        CMSAddr[i].sin_addr.s_addr = inet_addr(G_V.CMSIP[i]);
    }
    while(1){
      BWZJHeart    tempBW;
      time_t         nowT;
      struct tm        *t;
      time(&nowT);
      t=localtime(&nowT);
      tempBW.MsgID = MsgID++;
      tempBW.Year  = t->tm_year+1900;
      tempBW.Month = t->tm_mon+1;
      tempBW.Day   = t->tm_mday;
      tempBW.Hour  = t->tm_hour;
      tempBW.Min   = t->tm_min;
      tempBW.Sec   = t->tm_sec;
      tempBW.crc   = AT1_checksc(tempBW.Length-1, (const unsigned char*)&tempBW);
      for(int i=0;i<2;i++){
         sendto(socket_send,(char*)&tempBW,tempBW.Length,0,(struct sockaddr *)&CMSAddr[i],sizeof(sockaddr_in));
      }
        usleep(500000); 
    }
}

//接收数据库服务器心跳包
void *Thread_RecHeatBfromDBS(void *lpPara)
{
    byte *index;
    index=(byte *)lpPara;

    int DBSoc = 0;
    if ((DBSoc = socket(AF_INET, SOCK_DGRAM, 0)) < 0)
    {
        return ((void*)0);
    }
    struct sockaddr_in LocAddr;
    memset(&LocAddr, 0, sizeof(LocAddr));
    LocAddr.sin_family = AF_INET;
    LocAddr.sin_port = htons(12303);
    LocAddr.sin_addr.s_addr = inet_addr(G_V.LocalIP[*index]);
    if(bind(DBSoc, (struct sockaddr *)&LocAddr, sizeof(sockaddr)) < 0){
        return ((void*)0);
    }
    unsigned char RecBuf[512] = "";    
    while(1){
       int res = recv(DBSoc,RecBuf,sizeof(RecBuf),0);
       if(res>0 && RecBuf[0]==0x0C 
                && RecBuf[res-1] == 
                   AT1_checksc(res-1, (const unsigned char*)RecBuf) )
        {
          DB_Id[*index]=DB_Id[*index]+1;          
          //printf("%d :  %d\n",*index,DS_Id[*index]);
        }
        usleep(500000);
    }    
}

//检查数据库服务器到地算服务器心跳包
void *Thread_CheckHeatBfromDBS(void *lpPara)
{
   sleep(5);
   while(1){
       for(int i=0;i<2;i++){
         pre_DB_Id[i]= cur_DB_Id[i];
         cur_DB_Id[i] = DB_Id[i];
         if(cur_DB_Id[i] != pre_DB_Id[i])
            G_V.DBS_Stat[i]=true;
         else{ 
            G_V.DBS_Stat[i]=false;   
            char tbuf[512];
            sprintf(tbuf,"通道%2d,没有收到数据库服务器心跳包.");
            rk_syslog(tbuf);
         }
       }
       sleep(2); 
   }
}

//地算服务器到数据库服务心跳包
void* Thread_HeatBtoDBS(void *lpPara)
{
    struct sockaddr_in DBSAddr[2];
    int    socket_send;
    byte       MsgID=0;
    socket_send=socket(AF_INET,SOCK_DGRAM,0); //USE UDP
    for(int i=0;i<2;i++){
        memset(&DBSAddr[i], 0, sizeof(sockaddr_in));
        DBSAddr[i].sin_family = AF_INET;
        DBSAddr[i].sin_port = htons(12303);
        DBSAddr[i].sin_addr.s_addr = inet_addr(G_V.DBIP[i]);
    }
    while(1){
      BWZJHeart    tempBW;
      time_t         nowT;
      struct tm        *t;
      time(&nowT);
      t=localtime(&nowT);
      tempBW.MsgID = MsgID++;
      tempBW.Year  = t->tm_year+1900;
      tempBW.Month = t->tm_mon+1;
      tempBW.Day   = t->tm_mday;
      tempBW.Hour  = t->tm_hour;
      tempBW.Min   = t->tm_min;
      tempBW.Sec   = t->tm_sec;
      tempBW.crc   = AT1_checksc(tempBW.Length-1, (const unsigned char*)&tempBW);
      for(int i=0;i<2;i++){
         sendto(socket_send,(char*)&tempBW,tempBW.Length,0,(struct sockaddr *)&DBSAddr[i],sizeof(sockaddr_in));
      }
        usleep(500000); 
    }
}


//股道自动化信息接口线程，获取数据，压入队列
void *Thread_GetGdzdhInfo(void *lpPara)
{
    byte *index;
    index=(byte *)lpPara;
    int GDZDHSoc = 0;
    if ((GDZDHSoc = socket(AF_INET, SOCK_DGRAM, 0)) < 0)
    {
        return ((void*)0);
    }
    struct sockaddr_in LocAddr;
    memset(&LocAddr, 0, sizeof(LocAddr));
    LocAddr.sin_family = AF_INET;
    LocAddr.sin_port = htons(7777);
    LocAddr.sin_addr.s_addr = inet_addr(G_V.LocalIP[*index]);
    if(bind(GDZDHSoc, (struct sockaddr *)&LocAddr, sizeof(sockaddr)) < 0){
        return ((void*)0);
    }
    unsigned char RecBuf[5120] = "";
    while(1){
       int res = recv(GDZDHSoc,RecBuf,sizeof(RecBuf),0);
       if(RecBuf[0]==0xAA && RecBuf[1]==0xBB)
       {
            unsigned char crc = 0;
            for (int i = 0; i < res - 1; i++){
                crc = crc + RecBuf[i];
                if (crc == RecBuf[res - 1]) {
                    for (int i = 0; i < G_V.jwdBuf[*index].size(); i++) {
                        if (G_V.jwdBuf[*index][i].JWD_ID == (RecBuf[4] | RecBuf[5] << 8)) {
                            G_V.Mutex_gdzdh[*index][i].lock();
                            G_V.jwdBuf[*index][i].bufLen = res;
                            memcpy(G_V.jwdBuf[*index][i].jwdbuf, RecBuf,res);
                            G_V.jwdBuf[*index][i].timestamp = time(NULL);
                            G_V.Mutex_gdzdh[*index][i].unlock();
                            char tempstr[512]; 
                            sprintf(tempstr,"收到 JWD %d 的股道自动化信息(%d字节)",G_V.jwdBuf[*index][i].JWD_ID,res);
                            rk_syslog(tempstr);
                            break;
                        }
                    }
                }                
            }        
       }
       usleep(10);
   }
}