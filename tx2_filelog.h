#ifndef TX2_FILELOG_H
#define TX2_FILELOG_H
void rk_LogPathInitial(char *logpath);   //LogPath initial
void rk_syslog(char *logstr);   //Log the info str
int  tx2_cp(char *fromfile,char *tofile);
int tX2_CountLogFile(char *dirname);  //统计Log目录中的Log文件数量
int tX2_DeltheOldestLogFile(char *dirname );  //Delete the oldest file in the log dir
void DelLogMsg( char *dir_s);
int GetFreeSize(char *devname);
char * get_exe_path();
extern int logstat;
#endif // TX2_FILELOG_H
