cmake_minimum_required(VERSION 3.10)

project(DS_Service)

message("DS Service")

if (CMAKE_SYSTEM_PROCESSOR MATCHES "x86_64")
    set(ARCH_DIR x86)
else()
    set(ARCH_DIR arm)
endif()

set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS}")
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -std=c++11")


include_directories(
    inc
    /usr/include/mysql
)
link_directories(
   lib
)

set(link_libs  m dl pthread z mysqlclient)

add_executable(DSServ            main.cpp
                                  crc.cpp
                                  md5.cpp
                              readcfg.cpp
                             tinyxml2.cpp
                        udp_to_server.cpp
                           map_manage.cpp
                           rk_filelog.cpp
                            gdzdh_cal.cpp
                               ds_cal.cpp  
                )

target_link_libraries(DSServ ${link_libs})


