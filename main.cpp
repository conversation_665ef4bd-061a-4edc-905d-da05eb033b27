#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <sys/time.h>

#include <errno.h>
#include <signal.h>
#include <fcntl.h>
#include <unistd.h> 
#include <arpa/inet.h> 
#include <sys/socket.h>  
#include <netinet/in.h> 
#include <termios.h>
#include <pthread.h>
#include "DS_Data.h"
#include "zlib.h"
#include "udp_to_server.h"
#include "ds_cal.h"
#include "gdzdh_cal.h"
#include "readcfg.h"
#include "tx2_filelog.h"

StaticVar G_V;

void *Thread_ProcessMapQue(void *lpPara);
void *Thead_LoadMap(void *lpPara);

int main(int   argc,   char*   argv[])
{
    char tempbuf[512];
    int ch_net[2]={0,1};
    G_V.cmd_path = get_exe_path();
    sprintf(tempbuf,"%slog",G_V.cmd_path);
    rk_LogPathInitial(tempbuf); //初始化日志文件路径，启动日志线程

    sprintf(tempbuf,"%scfg/dssfig.xml",G_V.cmd_path);
    ReadXmlConfig(tempbuf);  

   strcpy(G_V.Map_Path,"/home/<USER>/ltdw/dss/bwc_map");
   
   
   if(LoadTrainType()==-1){
     rk_syslog("加载机车型号表失败.");
   }

   if(LoadGdzdhID()==-1){
     rk_syslog("加载股道自动化站场ID失败.");
   }

  //数据库服务器udp地址初始化
   for(int i=0;i<2;i++)
   {
      memset(&G_V.DBAddr[i], 0, sizeof(G_V.DBAddr[i]));
      G_V.DBAddr[i].sin_family = AF_INET;
      G_V.DBAddr[i].sin_port = htons(G_V.DBPort);
      G_V.DBAddr[i].sin_addr.s_addr = inet_addr((const char*)G_V.DBIP[i]);
   }

    pthread_t DS_tid[100];
    int t_index=0;
    //线程，周期性检查加载站场图索引文件
    pthread_create(&DS_tid[t_index++],NULL,Thead_LoadIDX,NULL);
    //产生两个线程用于接收通信服务器心跳包
    for(int i=0;i<2;i++){ 
        pthread_create(&DS_tid[t_index++],NULL,Thread_RecHeatBfromCMS,(void *)&ch_net[i]);
    }
    //产生线程用于检查通信服务到地算服务器心跳包
    pthread_create(&DS_tid[t_index++],NULL,Thread_CheckHeatBfromCMS,NULL);
    //产生线程用于发送地算服务器到通信服务器的心跳包
    pthread_create(&DS_tid[t_index++],NULL,Thread_HeatBtoCMS,NULL);


    //产生两个线程用于接收数据服务器心跳包
    for(int i=0;i<2;i++){ 
        pthread_create(&DS_tid[t_index++],NULL,Thread_RecHeatBfromDBS,(void *)&ch_net[i]);
    }
    //产生线程用于检查数据服务到地算服务器心跳包
    pthread_create(&DS_tid[t_index++],NULL,Thread_CheckHeatBfromDBS,NULL);
    //产生线程用于发送地算服务器到数据服务器的心跳包
    pthread_create(&DS_tid[t_index++],NULL,Thread_HeatBtoDBS,NULL);


    //产生线程用于向通信服务发送地算服务器业务包, 双通道发送UDP数据
    pthread_create(&DS_tid[t_index++],NULL,Thead_ForwardUDP,NULL);
    //产生线程用于接收通信服务发送的UDP数据,压入队列，双通道备份
    for(int i=0;i<2;i++){
         pthread_create(&DS_tid[t_index++],NULL,Thread_RecDatafromDSS,(void *)&ch_net[i]);
    }
    //产生线程用于处理通信服务数据队列，双通道备份
    for(int i=0;i<2;i++){
         pthread_create(&DS_tid[t_index++],NULL,Thread_ProcessCMSQue,(void *)&ch_net[i]);
    }
    //产生线程用接收股道自动化信息，双通道备份
    for(int i=0;i<2;i++){
         pthread_create(&DS_tid[t_index++],NULL,Thread_GetGdzdhInfo,(void *)&ch_net[i]);
    }
    //加载地图数据到内存线程
    pthread_create(&DS_tid[t_index++],NULL,Thead_LoadMap,NULL);
    
    //处理地图升级请求线程
    pthread_create(&DS_tid[t_index++],NULL,Thread_ProcessMapQue,NULL);

  	while(1)
	  {
       sleep(10);
    }
    return 0;
}


