# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.10

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/code_zys/DS_Service

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/code_zys/DS_Service/build

# Include any dependencies generated for this target.
include CMakeFiles/DSServ.dir/depend.make

# Include the progress variables for this target.
include CMakeFiles/DSServ.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/DSServ.dir/flags.make

CMakeFiles/DSServ.dir/main.cpp.o: CMakeFiles/DSServ.dir/flags.make
CMakeFiles/DSServ.dir/main.cpp.o: ../main.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/code_zys/DS_Service/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/DSServ.dir/main.cpp.o"
	/usr/bin/g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/DSServ.dir/main.cpp.o -c /home/<USER>/code_zys/DS_Service/main.cpp

CMakeFiles/DSServ.dir/main.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/DSServ.dir/main.cpp.i"
	/usr/bin/g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/code_zys/DS_Service/main.cpp > CMakeFiles/DSServ.dir/main.cpp.i

CMakeFiles/DSServ.dir/main.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/DSServ.dir/main.cpp.s"
	/usr/bin/g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/code_zys/DS_Service/main.cpp -o CMakeFiles/DSServ.dir/main.cpp.s

CMakeFiles/DSServ.dir/main.cpp.o.requires:

.PHONY : CMakeFiles/DSServ.dir/main.cpp.o.requires

CMakeFiles/DSServ.dir/main.cpp.o.provides: CMakeFiles/DSServ.dir/main.cpp.o.requires
	$(MAKE) -f CMakeFiles/DSServ.dir/build.make CMakeFiles/DSServ.dir/main.cpp.o.provides.build
.PHONY : CMakeFiles/DSServ.dir/main.cpp.o.provides

CMakeFiles/DSServ.dir/main.cpp.o.provides.build: CMakeFiles/DSServ.dir/main.cpp.o


CMakeFiles/DSServ.dir/crc.cpp.o: CMakeFiles/DSServ.dir/flags.make
CMakeFiles/DSServ.dir/crc.cpp.o: ../crc.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/code_zys/DS_Service/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object CMakeFiles/DSServ.dir/crc.cpp.o"
	/usr/bin/g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/DSServ.dir/crc.cpp.o -c /home/<USER>/code_zys/DS_Service/crc.cpp

CMakeFiles/DSServ.dir/crc.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/DSServ.dir/crc.cpp.i"
	/usr/bin/g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/code_zys/DS_Service/crc.cpp > CMakeFiles/DSServ.dir/crc.cpp.i

CMakeFiles/DSServ.dir/crc.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/DSServ.dir/crc.cpp.s"
	/usr/bin/g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/code_zys/DS_Service/crc.cpp -o CMakeFiles/DSServ.dir/crc.cpp.s

CMakeFiles/DSServ.dir/crc.cpp.o.requires:

.PHONY : CMakeFiles/DSServ.dir/crc.cpp.o.requires

CMakeFiles/DSServ.dir/crc.cpp.o.provides: CMakeFiles/DSServ.dir/crc.cpp.o.requires
	$(MAKE) -f CMakeFiles/DSServ.dir/build.make CMakeFiles/DSServ.dir/crc.cpp.o.provides.build
.PHONY : CMakeFiles/DSServ.dir/crc.cpp.o.provides

CMakeFiles/DSServ.dir/crc.cpp.o.provides.build: CMakeFiles/DSServ.dir/crc.cpp.o


CMakeFiles/DSServ.dir/md5.cpp.o: CMakeFiles/DSServ.dir/flags.make
CMakeFiles/DSServ.dir/md5.cpp.o: ../md5.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/code_zys/DS_Service/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object CMakeFiles/DSServ.dir/md5.cpp.o"
	/usr/bin/g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/DSServ.dir/md5.cpp.o -c /home/<USER>/code_zys/DS_Service/md5.cpp

CMakeFiles/DSServ.dir/md5.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/DSServ.dir/md5.cpp.i"
	/usr/bin/g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/code_zys/DS_Service/md5.cpp > CMakeFiles/DSServ.dir/md5.cpp.i

CMakeFiles/DSServ.dir/md5.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/DSServ.dir/md5.cpp.s"
	/usr/bin/g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/code_zys/DS_Service/md5.cpp -o CMakeFiles/DSServ.dir/md5.cpp.s

CMakeFiles/DSServ.dir/md5.cpp.o.requires:

.PHONY : CMakeFiles/DSServ.dir/md5.cpp.o.requires

CMakeFiles/DSServ.dir/md5.cpp.o.provides: CMakeFiles/DSServ.dir/md5.cpp.o.requires
	$(MAKE) -f CMakeFiles/DSServ.dir/build.make CMakeFiles/DSServ.dir/md5.cpp.o.provides.build
.PHONY : CMakeFiles/DSServ.dir/md5.cpp.o.provides

CMakeFiles/DSServ.dir/md5.cpp.o.provides.build: CMakeFiles/DSServ.dir/md5.cpp.o


CMakeFiles/DSServ.dir/readcfg.cpp.o: CMakeFiles/DSServ.dir/flags.make
CMakeFiles/DSServ.dir/readcfg.cpp.o: ../readcfg.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/code_zys/DS_Service/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building CXX object CMakeFiles/DSServ.dir/readcfg.cpp.o"
	/usr/bin/g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/DSServ.dir/readcfg.cpp.o -c /home/<USER>/code_zys/DS_Service/readcfg.cpp

CMakeFiles/DSServ.dir/readcfg.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/DSServ.dir/readcfg.cpp.i"
	/usr/bin/g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/code_zys/DS_Service/readcfg.cpp > CMakeFiles/DSServ.dir/readcfg.cpp.i

CMakeFiles/DSServ.dir/readcfg.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/DSServ.dir/readcfg.cpp.s"
	/usr/bin/g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/code_zys/DS_Service/readcfg.cpp -o CMakeFiles/DSServ.dir/readcfg.cpp.s

CMakeFiles/DSServ.dir/readcfg.cpp.o.requires:

.PHONY : CMakeFiles/DSServ.dir/readcfg.cpp.o.requires

CMakeFiles/DSServ.dir/readcfg.cpp.o.provides: CMakeFiles/DSServ.dir/readcfg.cpp.o.requires
	$(MAKE) -f CMakeFiles/DSServ.dir/build.make CMakeFiles/DSServ.dir/readcfg.cpp.o.provides.build
.PHONY : CMakeFiles/DSServ.dir/readcfg.cpp.o.provides

CMakeFiles/DSServ.dir/readcfg.cpp.o.provides.build: CMakeFiles/DSServ.dir/readcfg.cpp.o


CMakeFiles/DSServ.dir/tinyxml2.cpp.o: CMakeFiles/DSServ.dir/flags.make
CMakeFiles/DSServ.dir/tinyxml2.cpp.o: ../tinyxml2.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/code_zys/DS_Service/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building CXX object CMakeFiles/DSServ.dir/tinyxml2.cpp.o"
	/usr/bin/g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/DSServ.dir/tinyxml2.cpp.o -c /home/<USER>/code_zys/DS_Service/tinyxml2.cpp

CMakeFiles/DSServ.dir/tinyxml2.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/DSServ.dir/tinyxml2.cpp.i"
	/usr/bin/g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/code_zys/DS_Service/tinyxml2.cpp > CMakeFiles/DSServ.dir/tinyxml2.cpp.i

CMakeFiles/DSServ.dir/tinyxml2.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/DSServ.dir/tinyxml2.cpp.s"
	/usr/bin/g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/code_zys/DS_Service/tinyxml2.cpp -o CMakeFiles/DSServ.dir/tinyxml2.cpp.s

CMakeFiles/DSServ.dir/tinyxml2.cpp.o.requires:

.PHONY : CMakeFiles/DSServ.dir/tinyxml2.cpp.o.requires

CMakeFiles/DSServ.dir/tinyxml2.cpp.o.provides: CMakeFiles/DSServ.dir/tinyxml2.cpp.o.requires
	$(MAKE) -f CMakeFiles/DSServ.dir/build.make CMakeFiles/DSServ.dir/tinyxml2.cpp.o.provides.build
.PHONY : CMakeFiles/DSServ.dir/tinyxml2.cpp.o.provides

CMakeFiles/DSServ.dir/tinyxml2.cpp.o.provides.build: CMakeFiles/DSServ.dir/tinyxml2.cpp.o


CMakeFiles/DSServ.dir/udp_to_server.cpp.o: CMakeFiles/DSServ.dir/flags.make
CMakeFiles/DSServ.dir/udp_to_server.cpp.o: ../udp_to_server.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/code_zys/DS_Service/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Building CXX object CMakeFiles/DSServ.dir/udp_to_server.cpp.o"
	/usr/bin/g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/DSServ.dir/udp_to_server.cpp.o -c /home/<USER>/code_zys/DS_Service/udp_to_server.cpp

CMakeFiles/DSServ.dir/udp_to_server.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/DSServ.dir/udp_to_server.cpp.i"
	/usr/bin/g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/code_zys/DS_Service/udp_to_server.cpp > CMakeFiles/DSServ.dir/udp_to_server.cpp.i

CMakeFiles/DSServ.dir/udp_to_server.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/DSServ.dir/udp_to_server.cpp.s"
	/usr/bin/g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/code_zys/DS_Service/udp_to_server.cpp -o CMakeFiles/DSServ.dir/udp_to_server.cpp.s

CMakeFiles/DSServ.dir/udp_to_server.cpp.o.requires:

.PHONY : CMakeFiles/DSServ.dir/udp_to_server.cpp.o.requires

CMakeFiles/DSServ.dir/udp_to_server.cpp.o.provides: CMakeFiles/DSServ.dir/udp_to_server.cpp.o.requires
	$(MAKE) -f CMakeFiles/DSServ.dir/build.make CMakeFiles/DSServ.dir/udp_to_server.cpp.o.provides.build
.PHONY : CMakeFiles/DSServ.dir/udp_to_server.cpp.o.provides

CMakeFiles/DSServ.dir/udp_to_server.cpp.o.provides.build: CMakeFiles/DSServ.dir/udp_to_server.cpp.o


CMakeFiles/DSServ.dir/map_manage.cpp.o: CMakeFiles/DSServ.dir/flags.make
CMakeFiles/DSServ.dir/map_manage.cpp.o: ../map_manage.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/code_zys/DS_Service/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Building CXX object CMakeFiles/DSServ.dir/map_manage.cpp.o"
	/usr/bin/g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/DSServ.dir/map_manage.cpp.o -c /home/<USER>/code_zys/DS_Service/map_manage.cpp

CMakeFiles/DSServ.dir/map_manage.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/DSServ.dir/map_manage.cpp.i"
	/usr/bin/g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/code_zys/DS_Service/map_manage.cpp > CMakeFiles/DSServ.dir/map_manage.cpp.i

CMakeFiles/DSServ.dir/map_manage.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/DSServ.dir/map_manage.cpp.s"
	/usr/bin/g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/code_zys/DS_Service/map_manage.cpp -o CMakeFiles/DSServ.dir/map_manage.cpp.s

CMakeFiles/DSServ.dir/map_manage.cpp.o.requires:

.PHONY : CMakeFiles/DSServ.dir/map_manage.cpp.o.requires

CMakeFiles/DSServ.dir/map_manage.cpp.o.provides: CMakeFiles/DSServ.dir/map_manage.cpp.o.requires
	$(MAKE) -f CMakeFiles/DSServ.dir/build.make CMakeFiles/DSServ.dir/map_manage.cpp.o.provides.build
.PHONY : CMakeFiles/DSServ.dir/map_manage.cpp.o.provides

CMakeFiles/DSServ.dir/map_manage.cpp.o.provides.build: CMakeFiles/DSServ.dir/map_manage.cpp.o


CMakeFiles/DSServ.dir/rk_filelog.cpp.o: CMakeFiles/DSServ.dir/flags.make
CMakeFiles/DSServ.dir/rk_filelog.cpp.o: ../rk_filelog.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/code_zys/DS_Service/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_8) "Building CXX object CMakeFiles/DSServ.dir/rk_filelog.cpp.o"
	/usr/bin/g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/DSServ.dir/rk_filelog.cpp.o -c /home/<USER>/code_zys/DS_Service/rk_filelog.cpp

CMakeFiles/DSServ.dir/rk_filelog.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/DSServ.dir/rk_filelog.cpp.i"
	/usr/bin/g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/code_zys/DS_Service/rk_filelog.cpp > CMakeFiles/DSServ.dir/rk_filelog.cpp.i

CMakeFiles/DSServ.dir/rk_filelog.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/DSServ.dir/rk_filelog.cpp.s"
	/usr/bin/g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/code_zys/DS_Service/rk_filelog.cpp -o CMakeFiles/DSServ.dir/rk_filelog.cpp.s

CMakeFiles/DSServ.dir/rk_filelog.cpp.o.requires:

.PHONY : CMakeFiles/DSServ.dir/rk_filelog.cpp.o.requires

CMakeFiles/DSServ.dir/rk_filelog.cpp.o.provides: CMakeFiles/DSServ.dir/rk_filelog.cpp.o.requires
	$(MAKE) -f CMakeFiles/DSServ.dir/build.make CMakeFiles/DSServ.dir/rk_filelog.cpp.o.provides.build
.PHONY : CMakeFiles/DSServ.dir/rk_filelog.cpp.o.provides

CMakeFiles/DSServ.dir/rk_filelog.cpp.o.provides.build: CMakeFiles/DSServ.dir/rk_filelog.cpp.o


CMakeFiles/DSServ.dir/gdzdh_cal.cpp.o: CMakeFiles/DSServ.dir/flags.make
CMakeFiles/DSServ.dir/gdzdh_cal.cpp.o: ../gdzdh_cal.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/code_zys/DS_Service/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_9) "Building CXX object CMakeFiles/DSServ.dir/gdzdh_cal.cpp.o"
	/usr/bin/g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/DSServ.dir/gdzdh_cal.cpp.o -c /home/<USER>/code_zys/DS_Service/gdzdh_cal.cpp

CMakeFiles/DSServ.dir/gdzdh_cal.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/DSServ.dir/gdzdh_cal.cpp.i"
	/usr/bin/g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/code_zys/DS_Service/gdzdh_cal.cpp > CMakeFiles/DSServ.dir/gdzdh_cal.cpp.i

CMakeFiles/DSServ.dir/gdzdh_cal.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/DSServ.dir/gdzdh_cal.cpp.s"
	/usr/bin/g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/code_zys/DS_Service/gdzdh_cal.cpp -o CMakeFiles/DSServ.dir/gdzdh_cal.cpp.s

CMakeFiles/DSServ.dir/gdzdh_cal.cpp.o.requires:

.PHONY : CMakeFiles/DSServ.dir/gdzdh_cal.cpp.o.requires

CMakeFiles/DSServ.dir/gdzdh_cal.cpp.o.provides: CMakeFiles/DSServ.dir/gdzdh_cal.cpp.o.requires
	$(MAKE) -f CMakeFiles/DSServ.dir/build.make CMakeFiles/DSServ.dir/gdzdh_cal.cpp.o.provides.build
.PHONY : CMakeFiles/DSServ.dir/gdzdh_cal.cpp.o.provides

CMakeFiles/DSServ.dir/gdzdh_cal.cpp.o.provides.build: CMakeFiles/DSServ.dir/gdzdh_cal.cpp.o


CMakeFiles/DSServ.dir/ds_cal.cpp.o: CMakeFiles/DSServ.dir/flags.make
CMakeFiles/DSServ.dir/ds_cal.cpp.o: ../ds_cal.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/code_zys/DS_Service/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_10) "Building CXX object CMakeFiles/DSServ.dir/ds_cal.cpp.o"
	/usr/bin/g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/DSServ.dir/ds_cal.cpp.o -c /home/<USER>/code_zys/DS_Service/ds_cal.cpp

CMakeFiles/DSServ.dir/ds_cal.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/DSServ.dir/ds_cal.cpp.i"
	/usr/bin/g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/code_zys/DS_Service/ds_cal.cpp > CMakeFiles/DSServ.dir/ds_cal.cpp.i

CMakeFiles/DSServ.dir/ds_cal.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/DSServ.dir/ds_cal.cpp.s"
	/usr/bin/g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/code_zys/DS_Service/ds_cal.cpp -o CMakeFiles/DSServ.dir/ds_cal.cpp.s

CMakeFiles/DSServ.dir/ds_cal.cpp.o.requires:

.PHONY : CMakeFiles/DSServ.dir/ds_cal.cpp.o.requires

CMakeFiles/DSServ.dir/ds_cal.cpp.o.provides: CMakeFiles/DSServ.dir/ds_cal.cpp.o.requires
	$(MAKE) -f CMakeFiles/DSServ.dir/build.make CMakeFiles/DSServ.dir/ds_cal.cpp.o.provides.build
.PHONY : CMakeFiles/DSServ.dir/ds_cal.cpp.o.provides

CMakeFiles/DSServ.dir/ds_cal.cpp.o.provides.build: CMakeFiles/DSServ.dir/ds_cal.cpp.o


# Object files for target DSServ
DSServ_OBJECTS = \
"CMakeFiles/DSServ.dir/main.cpp.o" \
"CMakeFiles/DSServ.dir/crc.cpp.o" \
"CMakeFiles/DSServ.dir/md5.cpp.o" \
"CMakeFiles/DSServ.dir/readcfg.cpp.o" \
"CMakeFiles/DSServ.dir/tinyxml2.cpp.o" \
"CMakeFiles/DSServ.dir/udp_to_server.cpp.o" \
"CMakeFiles/DSServ.dir/map_manage.cpp.o" \
"CMakeFiles/DSServ.dir/rk_filelog.cpp.o" \
"CMakeFiles/DSServ.dir/gdzdh_cal.cpp.o" \
"CMakeFiles/DSServ.dir/ds_cal.cpp.o"

# External object files for target DSServ
DSServ_EXTERNAL_OBJECTS =

DSServ: CMakeFiles/DSServ.dir/main.cpp.o
DSServ: CMakeFiles/DSServ.dir/crc.cpp.o
DSServ: CMakeFiles/DSServ.dir/md5.cpp.o
DSServ: CMakeFiles/DSServ.dir/readcfg.cpp.o
DSServ: CMakeFiles/DSServ.dir/tinyxml2.cpp.o
DSServ: CMakeFiles/DSServ.dir/udp_to_server.cpp.o
DSServ: CMakeFiles/DSServ.dir/map_manage.cpp.o
DSServ: CMakeFiles/DSServ.dir/rk_filelog.cpp.o
DSServ: CMakeFiles/DSServ.dir/gdzdh_cal.cpp.o
DSServ: CMakeFiles/DSServ.dir/ds_cal.cpp.o
DSServ: CMakeFiles/DSServ.dir/build.make
DSServ: CMakeFiles/DSServ.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/home/<USER>/code_zys/DS_Service/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_11) "Linking CXX executable DSServ"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/DSServ.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/DSServ.dir/build: DSServ

.PHONY : CMakeFiles/DSServ.dir/build

CMakeFiles/DSServ.dir/requires: CMakeFiles/DSServ.dir/main.cpp.o.requires
CMakeFiles/DSServ.dir/requires: CMakeFiles/DSServ.dir/crc.cpp.o.requires
CMakeFiles/DSServ.dir/requires: CMakeFiles/DSServ.dir/md5.cpp.o.requires
CMakeFiles/DSServ.dir/requires: CMakeFiles/DSServ.dir/readcfg.cpp.o.requires
CMakeFiles/DSServ.dir/requires: CMakeFiles/DSServ.dir/tinyxml2.cpp.o.requires
CMakeFiles/DSServ.dir/requires: CMakeFiles/DSServ.dir/udp_to_server.cpp.o.requires
CMakeFiles/DSServ.dir/requires: CMakeFiles/DSServ.dir/map_manage.cpp.o.requires
CMakeFiles/DSServ.dir/requires: CMakeFiles/DSServ.dir/rk_filelog.cpp.o.requires
CMakeFiles/DSServ.dir/requires: CMakeFiles/DSServ.dir/gdzdh_cal.cpp.o.requires
CMakeFiles/DSServ.dir/requires: CMakeFiles/DSServ.dir/ds_cal.cpp.o.requires

.PHONY : CMakeFiles/DSServ.dir/requires

CMakeFiles/DSServ.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/DSServ.dir/cmake_clean.cmake
.PHONY : CMakeFiles/DSServ.dir/clean

CMakeFiles/DSServ.dir/depend:
	cd /home/<USER>/code_zys/DS_Service/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/code_zys/DS_Service /home/<USER>/code_zys/DS_Service /home/<USER>/code_zys/DS_Service/build /home/<USER>/code_zys/DS_Service/build /home/<USER>/code_zys/DS_Service/build/CMakeFiles/DSServ.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : CMakeFiles/DSServ.dir/depend

