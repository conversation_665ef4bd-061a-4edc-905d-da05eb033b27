#IncludeRegexLine: ^[ 	]*[#%][ 	]*(include|import)[ 	]*[<"]([^">]+)([">])

#IncludeRegexScan: ^.*$

#IncludeRegexComplain: ^$

#IncludeRegexTransform: 

../inc/zconf.h
stddef.h
-
windows.h
-
limits.h
-
sys/types.h
-
stdarg.h
-
stddef.h
-
unistd.h
-
unixio.h
-

../inc/zlib.h
zconf.h
../inc/zconf.h

/home/<USER>/code_zys/DS_Service/DS_Data.h
stdio.h
-
string.h
-
time.h
-
netinet/in.h
-
queue
-
vector
-
mutex
-

/home/<USER>/code_zys/DS_Service/crc.cpp
netinet/in.h
-

/home/<USER>/code_zys/DS_Service/crc.h

/home/<USER>/code_zys/DS_Service/ds_cal.cpp
stdio.h
-
stdlib.h
-
string.h
-
time.h
-
fcntl.h
-
unistd.h
-
arpa/inet.h
-
sys/socket.h
-
netinet/in.h
-
map
-
math.h
/home/<USER>/code_zys/DS_Service/math.h
tx2_filelog.h
/home/<USER>/code_zys/DS_Service/tx2_filelog.h
DS_Data.h
/home/<USER>/code_zys/DS_Service/DS_Data.h
udp_to_server.h
/home/<USER>/code_zys/DS_Service/udp_to_server.h
ds_station.h
/home/<USER>/code_zys/DS_Service/ds_station.h
crc.h
/home/<USER>/code_zys/DS_Service/crc.h
udp_to_server.h
/home/<USER>/code_zys/DS_Service/udp_to_server.h
gdzdh_cal.h
/home/<USER>/code_zys/DS_Service/gdzdh_cal.h
algorithm
-

/home/<USER>/code_zys/DS_Service/ds_cal.h

/home/<USER>/code_zys/DS_Service/ds_station.h
vector
-

/home/<USER>/code_zys/DS_Service/gdzdh_cal.cpp
DS_Data.h
/home/<USER>/code_zys/DS_Service/DS_Data.h
ds_station.h
/home/<USER>/code_zys/DS_Service/ds_station.h

/home/<USER>/code_zys/DS_Service/gdzdh_cal.h
ds_station.h
/home/<USER>/code_zys/DS_Service/ds_station.h

/home/<USER>/code_zys/DS_Service/main.cpp
stdio.h
-
stdlib.h
-
string.h
-
sys/time.h
-
errno.h
-
signal.h
-
fcntl.h
-
unistd.h
-
arpa/inet.h
-
sys/socket.h
-
netinet/in.h
-
termios.h
-
pthread.h
-
DS_Data.h
/home/<USER>/code_zys/DS_Service/DS_Data.h
zlib.h
/home/<USER>/code_zys/DS_Service/zlib.h
udp_to_server.h
/home/<USER>/code_zys/DS_Service/udp_to_server.h
ds_cal.h
/home/<USER>/code_zys/DS_Service/ds_cal.h
gdzdh_cal.h
/home/<USER>/code_zys/DS_Service/gdzdh_cal.h
readcfg.h
/home/<USER>/code_zys/DS_Service/readcfg.h
tx2_filelog.h
/home/<USER>/code_zys/DS_Service/tx2_filelog.h

/home/<USER>/code_zys/DS_Service/map_manage.cpp
sys/stat.h
-
fcntl.h
-
unistd.h
-
time.h
-
map
-
map_manage.h
/home/<USER>/code_zys/DS_Service/map_manage.h
DS_Data.h
/home/<USER>/code_zys/DS_Service/DS_Data.h
ds_cal.h
/home/<USER>/code_zys/DS_Service/ds_cal.h
md5.h
/home/<USER>/code_zys/DS_Service/md5.h
tx2_filelog.h
/home/<USER>/code_zys/DS_Service/tx2_filelog.h

/home/<USER>/code_zys/DS_Service/map_manage.h
string.h
-
netinet/in.h
-

/home/<USER>/code_zys/DS_Service/md5.cpp
md5.h
/home/<USER>/code_zys/DS_Service/md5.h
memory.h
-

/home/<USER>/code_zys/DS_Service/md5.h

/home/<USER>/code_zys/DS_Service/readcfg.cpp
DS_Data.h
/home/<USER>/code_zys/DS_Service/DS_Data.h
tinyxml2.h
/home/<USER>/code_zys/DS_Service/tinyxml2.h

/home/<USER>/code_zys/DS_Service/readcfg.h

/home/<USER>/code_zys/DS_Service/rk_filelog.cpp
stdio.h
-
unistd.h
-
iostream
-
string.h
-
sys/types.h
-
sys/stat.h
-
dirent.h
-
fcntl.h
-
thread
-
pthread.h
-
sys/time.h
-
queue
-
mutex
-

/home/<USER>/code_zys/DS_Service/tinyxml2.cpp
tinyxml2.h
/home/<USER>/code_zys/DS_Service/tinyxml2.h
new
-
stddef.h
-
stdarg.h
-
cstddef
-
cstdarg
-

/home/<USER>/code_zys/DS_Service/tinyxml2.h
ctype.h
-
limits.h
-
stdio.h
-
stdlib.h
-
string.h
-
stddef.h
-
cctype
-
climits
-
cstdio
-
cstdlib
-
cstring
-
stdint.h
-
android/log.h
-
assert.h
-

/home/<USER>/code_zys/DS_Service/tx2_filelog.h

/home/<USER>/code_zys/DS_Service/udp_to_server.cpp
stdio.h
-
stdlib.h
-
string.h
-
time.h
-
fcntl.h
-
unistd.h
-
arpa/inet.h
-
sys/socket.h
-
sys/time.h
-
netinet/in.h
-
iconv.h
-
map
-
mutex
-
zlib.h
/home/<USER>/code_zys/DS_Service/zlib.h
DS_Data.h
/home/<USER>/code_zys/DS_Service/DS_Data.h
crc.h
/home/<USER>/code_zys/DS_Service/crc.h
tx2_filelog.h
/home/<USER>/code_zys/DS_Service/tx2_filelog.h

/home/<USER>/code_zys/DS_Service/udp_to_server.h

