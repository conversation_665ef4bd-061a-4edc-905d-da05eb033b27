# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.10

# Default target executed when no arguments are given to make.
default_target: all

.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/code_zys/DS_Service

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/code_zys/DS_Service/build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake -H$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache

.PHONY : rebuild_cache/fast

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "No interactive CMake dialog available..."
	/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache

.PHONY : edit_cache/fast

# The main all target
all: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/code_zys/DS_Service/build/CMakeFiles /home/<USER>/code_zys/DS_Service/build/CMakeFiles/progress.marks
	$(MAKE) -f CMakeFiles/Makefile2 all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/code_zys/DS_Service/build/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	$(MAKE) -f CMakeFiles/Makefile2 clean
.PHONY : clean

# The main clean target
clean/fast: clean

.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	$(MAKE) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	$(MAKE) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	$(CMAKE_COMMAND) -H$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

#=============================================================================
# Target rules for targets named DSServ

# Build rule for target.
DSServ: cmake_check_build_system
	$(MAKE) -f CMakeFiles/Makefile2 DSServ
.PHONY : DSServ

# fast build rule for target.
DSServ/fast:
	$(MAKE) -f CMakeFiles/DSServ.dir/build.make CMakeFiles/DSServ.dir/build
.PHONY : DSServ/fast

crc.o: crc.cpp.o

.PHONY : crc.o

# target to build an object file
crc.cpp.o:
	$(MAKE) -f CMakeFiles/DSServ.dir/build.make CMakeFiles/DSServ.dir/crc.cpp.o
.PHONY : crc.cpp.o

crc.i: crc.cpp.i

.PHONY : crc.i

# target to preprocess a source file
crc.cpp.i:
	$(MAKE) -f CMakeFiles/DSServ.dir/build.make CMakeFiles/DSServ.dir/crc.cpp.i
.PHONY : crc.cpp.i

crc.s: crc.cpp.s

.PHONY : crc.s

# target to generate assembly for a file
crc.cpp.s:
	$(MAKE) -f CMakeFiles/DSServ.dir/build.make CMakeFiles/DSServ.dir/crc.cpp.s
.PHONY : crc.cpp.s

ds_cal.o: ds_cal.cpp.o

.PHONY : ds_cal.o

# target to build an object file
ds_cal.cpp.o:
	$(MAKE) -f CMakeFiles/DSServ.dir/build.make CMakeFiles/DSServ.dir/ds_cal.cpp.o
.PHONY : ds_cal.cpp.o

ds_cal.i: ds_cal.cpp.i

.PHONY : ds_cal.i

# target to preprocess a source file
ds_cal.cpp.i:
	$(MAKE) -f CMakeFiles/DSServ.dir/build.make CMakeFiles/DSServ.dir/ds_cal.cpp.i
.PHONY : ds_cal.cpp.i

ds_cal.s: ds_cal.cpp.s

.PHONY : ds_cal.s

# target to generate assembly for a file
ds_cal.cpp.s:
	$(MAKE) -f CMakeFiles/DSServ.dir/build.make CMakeFiles/DSServ.dir/ds_cal.cpp.s
.PHONY : ds_cal.cpp.s

gdzdh_cal.o: gdzdh_cal.cpp.o

.PHONY : gdzdh_cal.o

# target to build an object file
gdzdh_cal.cpp.o:
	$(MAKE) -f CMakeFiles/DSServ.dir/build.make CMakeFiles/DSServ.dir/gdzdh_cal.cpp.o
.PHONY : gdzdh_cal.cpp.o

gdzdh_cal.i: gdzdh_cal.cpp.i

.PHONY : gdzdh_cal.i

# target to preprocess a source file
gdzdh_cal.cpp.i:
	$(MAKE) -f CMakeFiles/DSServ.dir/build.make CMakeFiles/DSServ.dir/gdzdh_cal.cpp.i
.PHONY : gdzdh_cal.cpp.i

gdzdh_cal.s: gdzdh_cal.cpp.s

.PHONY : gdzdh_cal.s

# target to generate assembly for a file
gdzdh_cal.cpp.s:
	$(MAKE) -f CMakeFiles/DSServ.dir/build.make CMakeFiles/DSServ.dir/gdzdh_cal.cpp.s
.PHONY : gdzdh_cal.cpp.s

main.o: main.cpp.o

.PHONY : main.o

# target to build an object file
main.cpp.o:
	$(MAKE) -f CMakeFiles/DSServ.dir/build.make CMakeFiles/DSServ.dir/main.cpp.o
.PHONY : main.cpp.o

main.i: main.cpp.i

.PHONY : main.i

# target to preprocess a source file
main.cpp.i:
	$(MAKE) -f CMakeFiles/DSServ.dir/build.make CMakeFiles/DSServ.dir/main.cpp.i
.PHONY : main.cpp.i

main.s: main.cpp.s

.PHONY : main.s

# target to generate assembly for a file
main.cpp.s:
	$(MAKE) -f CMakeFiles/DSServ.dir/build.make CMakeFiles/DSServ.dir/main.cpp.s
.PHONY : main.cpp.s

map_manage.o: map_manage.cpp.o

.PHONY : map_manage.o

# target to build an object file
map_manage.cpp.o:
	$(MAKE) -f CMakeFiles/DSServ.dir/build.make CMakeFiles/DSServ.dir/map_manage.cpp.o
.PHONY : map_manage.cpp.o

map_manage.i: map_manage.cpp.i

.PHONY : map_manage.i

# target to preprocess a source file
map_manage.cpp.i:
	$(MAKE) -f CMakeFiles/DSServ.dir/build.make CMakeFiles/DSServ.dir/map_manage.cpp.i
.PHONY : map_manage.cpp.i

map_manage.s: map_manage.cpp.s

.PHONY : map_manage.s

# target to generate assembly for a file
map_manage.cpp.s:
	$(MAKE) -f CMakeFiles/DSServ.dir/build.make CMakeFiles/DSServ.dir/map_manage.cpp.s
.PHONY : map_manage.cpp.s

md5.o: md5.cpp.o

.PHONY : md5.o

# target to build an object file
md5.cpp.o:
	$(MAKE) -f CMakeFiles/DSServ.dir/build.make CMakeFiles/DSServ.dir/md5.cpp.o
.PHONY : md5.cpp.o

md5.i: md5.cpp.i

.PHONY : md5.i

# target to preprocess a source file
md5.cpp.i:
	$(MAKE) -f CMakeFiles/DSServ.dir/build.make CMakeFiles/DSServ.dir/md5.cpp.i
.PHONY : md5.cpp.i

md5.s: md5.cpp.s

.PHONY : md5.s

# target to generate assembly for a file
md5.cpp.s:
	$(MAKE) -f CMakeFiles/DSServ.dir/build.make CMakeFiles/DSServ.dir/md5.cpp.s
.PHONY : md5.cpp.s

readcfg.o: readcfg.cpp.o

.PHONY : readcfg.o

# target to build an object file
readcfg.cpp.o:
	$(MAKE) -f CMakeFiles/DSServ.dir/build.make CMakeFiles/DSServ.dir/readcfg.cpp.o
.PHONY : readcfg.cpp.o

readcfg.i: readcfg.cpp.i

.PHONY : readcfg.i

# target to preprocess a source file
readcfg.cpp.i:
	$(MAKE) -f CMakeFiles/DSServ.dir/build.make CMakeFiles/DSServ.dir/readcfg.cpp.i
.PHONY : readcfg.cpp.i

readcfg.s: readcfg.cpp.s

.PHONY : readcfg.s

# target to generate assembly for a file
readcfg.cpp.s:
	$(MAKE) -f CMakeFiles/DSServ.dir/build.make CMakeFiles/DSServ.dir/readcfg.cpp.s
.PHONY : readcfg.cpp.s

rk_filelog.o: rk_filelog.cpp.o

.PHONY : rk_filelog.o

# target to build an object file
rk_filelog.cpp.o:
	$(MAKE) -f CMakeFiles/DSServ.dir/build.make CMakeFiles/DSServ.dir/rk_filelog.cpp.o
.PHONY : rk_filelog.cpp.o

rk_filelog.i: rk_filelog.cpp.i

.PHONY : rk_filelog.i

# target to preprocess a source file
rk_filelog.cpp.i:
	$(MAKE) -f CMakeFiles/DSServ.dir/build.make CMakeFiles/DSServ.dir/rk_filelog.cpp.i
.PHONY : rk_filelog.cpp.i

rk_filelog.s: rk_filelog.cpp.s

.PHONY : rk_filelog.s

# target to generate assembly for a file
rk_filelog.cpp.s:
	$(MAKE) -f CMakeFiles/DSServ.dir/build.make CMakeFiles/DSServ.dir/rk_filelog.cpp.s
.PHONY : rk_filelog.cpp.s

tinyxml2.o: tinyxml2.cpp.o

.PHONY : tinyxml2.o

# target to build an object file
tinyxml2.cpp.o:
	$(MAKE) -f CMakeFiles/DSServ.dir/build.make CMakeFiles/DSServ.dir/tinyxml2.cpp.o
.PHONY : tinyxml2.cpp.o

tinyxml2.i: tinyxml2.cpp.i

.PHONY : tinyxml2.i

# target to preprocess a source file
tinyxml2.cpp.i:
	$(MAKE) -f CMakeFiles/DSServ.dir/build.make CMakeFiles/DSServ.dir/tinyxml2.cpp.i
.PHONY : tinyxml2.cpp.i

tinyxml2.s: tinyxml2.cpp.s

.PHONY : tinyxml2.s

# target to generate assembly for a file
tinyxml2.cpp.s:
	$(MAKE) -f CMakeFiles/DSServ.dir/build.make CMakeFiles/DSServ.dir/tinyxml2.cpp.s
.PHONY : tinyxml2.cpp.s

udp_to_server.o: udp_to_server.cpp.o

.PHONY : udp_to_server.o

# target to build an object file
udp_to_server.cpp.o:
	$(MAKE) -f CMakeFiles/DSServ.dir/build.make CMakeFiles/DSServ.dir/udp_to_server.cpp.o
.PHONY : udp_to_server.cpp.o

udp_to_server.i: udp_to_server.cpp.i

.PHONY : udp_to_server.i

# target to preprocess a source file
udp_to_server.cpp.i:
	$(MAKE) -f CMakeFiles/DSServ.dir/build.make CMakeFiles/DSServ.dir/udp_to_server.cpp.i
.PHONY : udp_to_server.cpp.i

udp_to_server.s: udp_to_server.cpp.s

.PHONY : udp_to_server.s

# target to generate assembly for a file
udp_to_server.cpp.s:
	$(MAKE) -f CMakeFiles/DSServ.dir/build.make CMakeFiles/DSServ.dir/udp_to_server.cpp.s
.PHONY : udp_to_server.cpp.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... rebuild_cache"
	@echo "... DSServ"
	@echo "... edit_cache"
	@echo "... crc.o"
	@echo "... crc.i"
	@echo "... crc.s"
	@echo "... ds_cal.o"
	@echo "... ds_cal.i"
	@echo "... ds_cal.s"
	@echo "... gdzdh_cal.o"
	@echo "... gdzdh_cal.i"
	@echo "... gdzdh_cal.s"
	@echo "... main.o"
	@echo "... main.i"
	@echo "... main.s"
	@echo "... map_manage.o"
	@echo "... map_manage.i"
	@echo "... map_manage.s"
	@echo "... md5.o"
	@echo "... md5.i"
	@echo "... md5.s"
	@echo "... readcfg.o"
	@echo "... readcfg.i"
	@echo "... readcfg.s"
	@echo "... rk_filelog.o"
	@echo "... rk_filelog.i"
	@echo "... rk_filelog.s"
	@echo "... tinyxml2.o"
	@echo "... tinyxml2.i"
	@echo "... tinyxml2.s"
	@echo "... udp_to_server.o"
	@echo "... udp_to_server.i"
	@echo "... udp_to_server.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -H$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

