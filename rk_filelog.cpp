#include <stdio.h>
#include <unistd.h>
#include <iostream>
#include <string.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <dirent.h>
#include <fcntl.h>
#include <thread>
#include <pthread.h>
#include <sys/time.h>
#include <queue>
#include <mutex>
#define M_PATH_MAX 1024

using namespace std;

queue<string> log_queue;
mutex mtxQueueLog;               // mutex of log queue

char pathandfilename[200];
char fiename[50]="syslog";
pthread_t log_thread;

#define logfile_N  3  //Max Number of logfile in logpath;

int logstat=0;
int tX2_CountLogFile(char *dirname)  //统计Log目录中的Log文件数量
{
 DIR *dirp;
 struct dirent *dp;
 int i;
 dirp=opendir(dirname);
 if(dirp==NULL) {
   printf("%s dir do not exists\n",dirname);
   return -1;
 }
 for(i=0;;)
 {
   dp=readdir(dirp);
   if(dp==NULL)
       break;
   if(strcmp(dp->d_name,".")==0 || strcmp(dp->d_name,"..")==0)
       continue;
    i=i+1;
    //printf("%s\n", dp->d_name);
 }

 return i;
}


int tX2_DeltheOldestLogFile(char *dirname )  //Delete the oldest file in the log dir
{
 DIR *dirp;
 char oldestfile[100],tempfile[100];
 struct dirent *dp;
 struct stat tempfile_stat;
 FILE *fp;
 int fs=0;
 int st=0;
 time_t oldest_tt;
 //time(&oldest_tt);
 //strcpy(oldestfile,"zys");
 dirp=opendir(dirname);
 if(dirp==NULL) {
   printf("%s dir do not exists\n",dirname);
   return -1;
 }
 int i=0;
 while(1)
 {
   dp=readdir(dirp);
   if(dp==NULL)
       break;
   if(strcmp(dp->d_name,".")==0 || strcmp(dp->d_name,"..")==0)
       continue;
    sprintf(tempfile,"%s/%s", dirname,dp->d_name);
    stat(tempfile,&tempfile_stat);
    if(i==0){
        oldest_tt=tempfile_stat.st_mtime;
        strcpy(oldestfile,tempfile);
        fs=1;
    }else{
        if(tempfile_stat.st_mtime < oldest_tt){
           oldest_tt=tempfile_stat.st_mtime;
           strcpy(oldestfile,tempfile);
           fs=1;
        }
    }
    i=i+1;
 }
 if(fs ==1){
     char cmdstr[100];
     sprintf(cmdstr,"sudo rm -r -f %s",oldestfile);
     fp=popen(cmdstr,"r");
     pclose(fp);
 }
 return fs;
}


void *rk_log_thread(void *arg)
{
  string logstr;
  char logbuf[300];
  while(1){
      mtxQueueLog.lock();
      if(!log_queue.empty()) {

           logstr=log_queue.front();
           log_queue.pop();
           mtxQueueLog.unlock();
           char* chr = const_cast<char*>(logstr.c_str());
           struct tm *t;
            time_t tt;
            time(&tt);
            t=localtime(&tt);

            sprintf(logbuf,"%4d-%02d-%02d %02d:%02d:%02d       %s\n",t->tm_year+1900,t->tm_mon+1,t->tm_mday,
                                                            t->tm_hour,t->tm_min,t->tm_sec,chr);
            printf("%s",logbuf);
            FILE *fp;
            fp=fopen(pathandfilename,"a");
            if(fp==NULL) break;
            fputs(logbuf, fp);
            fclose(fp);
            struct stat statbuff;
            stat(pathandfilename, &statbuff);
            if(statbuff.st_size>2097152)
            {

               char newname[200],tbuf[50];
               sprintf(tbuf,"%4d%02d%02d%02d%02d%02d",t->tm_year+1900,t->tm_mon+1,t->tm_mday,
                                                             t->tm_hour,t->tm_min,t->tm_sec);
               sprintf(newname,"%s_%s",pathandfilename, tbuf);
               rename(pathandfilename, newname);

            }


       }else{
          mtxQueueLog.unlock();
          continue;
      }


  } //end while loop

}


void rk_LogPathInitial(char *logpath)   //LogPath initial
{
  DIR *dp;
  if ((dp = opendir(logpath)) == NULL)
  {
       mkdir(logpath, S_IRWXU | S_IRWXG | S_IROTH | S_IXOTH);
  }
  while(tX2_CountLogFile(logpath)>3)
  {
       tX2_DeltheOldestLogFile(logpath);
  }
  sprintf(pathandfilename,"%s/%s",logpath,fiename);
  pthread_create(&log_thread,NULL,rk_log_thread,(void *)NULL);
  pthread_detach(log_thread);
  logstat=1;
}


void rk_syslog(char *logstr)   //Log the info str
{
   string str = logstr;
   mtxQueueLog.lock();
   if(logstat) log_queue.push(str);
   mtxQueueLog.unlock();
}

void DelLogMsg( char *dir_s)
{
    FILE *fp;
    char cmdbuf[512];
    sprintf(cmdbuf,"sudo rm %s/*.xz",dir_s);
    fp = popen(cmdbuf,"r");
    fclose(fp);
    sprintf(cmdbuf,"sudo rm %s/*.old",dir_s);
    fp = popen(cmdbuf,"r");
    fclose(fp);
    sprintf(cmdbuf,"echo \"\" | sudo tee /%s/*",dir_s);
    fp = popen(cmdbuf,"r");
    fclose(fp);
}

int GetFreeSize(char *devname)
{
  FILE *fp;
  char temp[512],zzz[512],result[100];
  int PosA=0,PosB=0;
  sprintf(temp,"df |grep %s",devname);
  fp = popen(temp,"r");
  fgets(zzz, 500, fp);
  fclose(fp);
  for(int i=0;i<strlen(zzz);i++){
     if (zzz[i]=='%') {
         PosB=i;
         break;
      }
  }
  for(int i=0;i<PosB;i++){
     if (zzz[i]==' ') {
         PosA=i;
      }
  }

  for(int i=PosA+1;i<PosB;i++){
      result[i-PosA-1]=zzz[i];
  }
  result[PosB-PosA-1]='\0';
  return atoi(result);
}

char * get_exe_path()
{
    static char buf[M_PATH_MAX];
    int i;
    int rslt = readlink("/proc/self/exe", buf, M_PATH_MAX);
    if (rslt < 0 || rslt >= M_PATH_MAX)
    {
        return NULL;
    }
    buf[rslt] = '\0';
    for (i = rslt; i >= 0; i--)
    {
        if (buf[i] == '/')
        {
            buf[i + 1] = '\0';
            break;
        }
    }
    return buf;
}
