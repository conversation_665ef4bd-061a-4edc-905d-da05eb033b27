//股道自动化计算模块
#include "DS_Data.h"
#include "ds_station.h"
extern StaticVar G_V;

char *makeword(char *line, char stop);
//加解密函数
void m_crypt(unsigned char* sbuf, unsigned char* tbuf, int len)
{
	unsigned char key1 = 217, key2 = 21;
	for (int i = 0; i < len; i++) {
		tbuf[i] = sbuf[i] ^ (key1 + i) ^ (key2 + i);
	}
}

//加载股道自动化站场ID
int LoadGdzdhID()
{
   FILE* fp;
   char fbuf[1024]; 
   sprintf(fbuf,"%scfg/jwd_id.ini",G_V.cmd_path);
   fp = fopen(fbuf, "r");
   if(fp==NULL) return -1;
   while(1){
  		char buf[128];
		if (feof(fp)) break;
		fgets(buf, 128, fp);
        if(strchr(buf,',')!=NULL){
            char *idBuf;
            idBuf=makeword(buf, ',');
            gdzdhBuf gbuf;
            gbuf.bufLen=0;
            gbuf.JWD_ID=atoi(idBuf);
            G_V.jwdBuf[0].push_back(gbuf);
            G_V.jwdBuf[1].push_back(gbuf);
            free(idBuf);
        }
   }
   fclose(fp);        
   return 0;
}

//获取相应站场的股道自动化信息
int GetGdzdhID(int stationID,int netID)
{
	int stat =-1;
	return -1;
	for(int i=0;i<G_V.jwdBuf[netID].size();i++){
		if( G_V.jwdBuf[netID][i].JWD_ID == stationID){
			stat =i;
			break;
		}
	}
	if(stat==-1) return -1;
	int temppro=time(NULL)-G_V.jwdBuf[netID][stat].timestamp;
	if(temppro < 0 || temppro > 10) stat=-2;
	return stat;
}

//获取股道自动化信号灯、道岔、股道和机车状态
int GetGdzdhInfo(char* buf, int res,DStation* st_map)
{
	//获取信号灯状态
	int start_i = 0;
	uint16_t g_totalLamp = buf[start_i] | buf[start_i+1] << 8;
	start_i = start_i + 2;
	for (int i = 0; i < g_totalLamp; i++) {
		if (start_i + 3 * i + 2 > res - 1) return 0;
		uint16_t lampID = buf[start_i + 3 * i] | buf[start_i + 3 * i + 1] << 8;
		int tt= buf[start_i + 3 * i + 2];
		if(tt==1)
           st_map->Lamps[lampID - 1].lampvalue = 2;
		else if(tt==2)
		   st_map->Lamps[lampID - 1].lampvalue = 1;
	}
	//获取道岔状态
	start_i = start_i + 3 * g_totalLamp;
	if ((start_i +1) > (res - 1)) return 0;
	uint16_t g_totalSwitch = buf[start_i] | buf[start_i+1] << 8;
	start_i = start_i + 2;

	for (int i = 0; i < g_totalSwitch; i++) {
		if (start_i + 3 * i + 2 > (res - 1)) return 0;
		uint16_t switchID = buf[start_i + 3 * i] | buf[start_i + 3 * i + 1] << 8;
        st_map->Turnouts[switchID - 1].stat = buf[start_i + 3 * i + 2];
	}
	//获取股道状态
	start_i = start_i + 3 * g_totalSwitch;
	if (start_i+1 > (res - 1)) return 0;
	uint16_t  g_total_Railway = buf[start_i] | buf[start_i + 1] << 8;
	start_i = start_i + 2;
	for (int i = 0; i < g_total_Railway; i++) {
		if (start_i + 3 * i + 2 > (res - 1)) return 0;
		uint16_t railID = buf[start_i + 3 * i] | buf[start_i + 3 * i + 1] << 8;
	    st_map->Railways[railID - 1].Stat = buf[start_i + 3 * i + 2];
	}
	//获取机车状态 gdzdhTrain[]
	st_map->GdzdhTrains.clear();
	start_i = start_i + 3 * g_total_Railway;
	if (start_i > (res - 1)) return start_i;
	uint16_t  g_totalTrain = buf[start_i] ;
	start_i = start_i + 1;
	for (int i = 0; i < g_totalTrain; i++) {
		if ((start_i + 1 + 1 + buf[start_i + 2]) > res - 1) break;
		Train_Stat gTrain;
		gTrain.railwayID = buf[start_i] | buf[start_i + 1] << 8;
		memcpy(gTrain.TName, &buf[start_i + 3], buf[start_i + 2]);
		st_map->GdzdhTrains.push_back(gTrain);
		start_i = start_i + 1 + 1 + buf[start_i + 2]+1;
	}
	return start_i;
}

//根据道岔状态确定径路
void GetRailPath(DStation* st_map)
{
	for (int i = 0; i < st_map->Rail_N; i++) {
		if (st_map->Railways_initial[i].nextLineN > 1) {//根据道岔状态在前方两条线路中选取一条
               
			if (st_map->Railways[i].totalTurnoutN == 1){//线路仅一个道岔
				if (st_map->Turnouts[st_map->Railways[i].Turnouts[0]].stat==1)//道岔:  1 定位
				{
					st_map->Railways[i].nextLineN = 1;
					st_map->Railways[i].nextLineIDs[0] = st_map->Railways_initial[i].nextLineIDs[0];
				}
				else if (st_map->Turnouts[st_map->Railways[i].Turnouts[0]].stat == 2)//道岔:  1 反位
				{
					st_map->Railways[i].nextLineN = 1;
					st_map->Railways[i].nextLineIDs[0] = st_map->Railways_initial[i].nextLineIDs[1];
				}
				else {
					st_map->Railways[i].nextLineN = st_map->Railways_initial[i].nextLineN;
					st_map->Railways[i].nextLineIDs[0] = st_map->Railways_initial[i].nextLineIDs[0];
					st_map->Railways[i].nextLineIDs[1] = st_map->Railways_initial[i].nextLineIDs[1];
				}

			}
			else if(st_map->Railways[i].totalTurnoutN == 2){//线路两个道岔
			    
				if (st_map->Turnouts[st_map->Railways[i].Turnouts[1]].stat == 1) //道岔:  1 定位
				{
					st_map->Railways[i].nextLineN = 1;
					st_map->Railways[i].nextLineIDs[0] = st_map->Railways_initial[i].nextLineIDs[0];
				}
				else if (st_map->Turnouts[st_map->Railways[i].Turnouts[1]].stat == 2)//道岔:  1 反位
				{
					st_map->Railways[i].nextLineN = 1;
					st_map->Railways[i].nextLineIDs[0] = st_map->Railways_initial[i].nextLineIDs[1];
				}
				else {
					st_map->Railways[i].nextLineN = st_map->Railways_initial[i].nextLineN;
					st_map->Railways[i].nextLineIDs[0] = st_map->Railways_initial[i].nextLineIDs[0];
					st_map->Railways[i].nextLineIDs[1] = st_map->Railways_initial[i].nextLineIDs[1];
				}
			}
		}
        
		if (st_map->Railways_initial[i].preLineN > 1) {//根据道岔状态在前方两条线路中选取一条
		
				if (st_map->Turnouts[st_map->Railways[i].Turnouts[0]].stat == 1)//道岔:  1 定位
				{
					st_map->Railways[i].preLineN = 1;
					st_map->Railways[i].preLineIDs[0] = st_map->Railways_initial[i].preLineIDs[0];
				}
				else if (st_map->Turnouts[st_map->Railways[i].Turnouts[0]].stat == 2)//道岔:  1 反位
				{
					st_map->Railways[i].preLineN = 1;
					st_map->Railways[i].preLineIDs[0] = st_map->Railways_initial[i].preLineIDs[1];
				}
				else {
					st_map->Railways[i].preLineN = st_map->Railways_initial[i].preLineN;
					st_map->Railways[i].preLineIDs[0] = st_map->Railways_initial[i].preLineIDs[0];
					st_map->Railways[i].preLineIDs[1] = st_map->Railways_initial[i].preLineIDs[1];
				}
		}

    }
	return;
}

void RestoreRailPath(DStation* st_map)
{
   for (int i = 0; i < st_map->Rail_N; i++) {//股道逻辑关系恢复初始状态
        st_map->Railways[i].preLineN = st_map->Railways_initial[i].preLineN;
		for(int j=0;j<st_map->Railways[i].preLineN;j++){
			st_map->Railways[i].preLineIDs[j] = st_map->Railways_initial[i].preLineIDs[j];
		}
		st_map->Railways[i].nextLineN = st_map->Railways_initial[i].nextLineN;
		for (int j = 0; j < st_map->Railways[i].nextLineN; j++) {
			st_map->Railways[i].nextLineIDs[j] = st_map->Railways_initial[i].nextLineIDs[j];
		}
    }
	for(int i=0;i<st_map->Turnout_N;i++){//道岔恢复初始状态
		st_map->Turnouts[i].stat = 0;
	}
    for(int i=0;i<st_map->Lamp_N;i++){//信号灯恢复初始状态
		st_map->Lamps[i].lampvalue = 0;
	}
}