#include "DS_Data.h"
#include "tinyxml2.h"
extern StaticVar G_V;


char *makeword(char *line, char stop)
{
    int x = 0, y;
    char *word = (char *)malloc(sizeof(char)*(strlen(line) + 1));
    for (x = 0; ((line[x]) && (line[x] != stop)); x++)
        word[x] = line[x];
    word[x] = '\0';
    if (line[x]) ++x;
    y = 0;
    while (line[y++] = line[x++]);
    return word;

}

int ReadXmlConfig(char *xmlFile)
{
   tinyxml2::XMLDocument doc;
   tinyxml2::XMLError ret = doc.LoadFile(xmlFile);
   if (ret != 0) {
       printf("加载配置文件失败.\n");
       return -1;
   }
   tinyxml2::XMLElement* root = doc.RootElement();

   tinyxml2::XMLElement* Local = root->FirstChildElement("Local");
   if (!Local) {
        printf("加载配置文件失败.\n");
        return -1;
   }

   tinyxml2::XMLElement* IP0 = Local->FirstChildElement("IP0");
   if (!IP0) {
        printf("本地配置缺失IP0.");
        return -1;
   }
   strcpy(G_V.LocalIP[0],IP0->GetText());

   tinyxml2::XMLElement* IP1 = Local->FirstChildElement("IP1");
   if (!IP1) {
        printf("本地配置缺失IP1.");
        return -1;
   }
   strcpy(G_V.LocalIP[1],IP1->GetText());

   tinyxml2::XMLElement* CMServer = root->FirstChildElement("CMServer");
   if (!CMServer) {
        printf("缺失通信服务器配置.");
        return -1;
   }
   IP0 = CMServer->FirstChildElement("IP0");
   if (!IP0) {
           printf("通信服务器缺失IP0.");
           return -1;
   }
   strcpy(G_V.CMSIP[0],IP0->GetText());
   IP1 = CMServer->FirstChildElement("IP1");
   if (!IP1) {
           printf("通信服务器缺失IP1.");
           return -1;
   }
   strcpy(G_V.CMSIP[1],IP1->GetText());
   tinyxml2::XMLElement* UDPPort = CMServer->FirstChildElement("UDPPort");
   if (!UDPPort) {
           printf("通信服务器缺失端口配置.");
           return -1;
   }
   G_V.CMSPort = atoi(UDPPort->GetText());

   tinyxml2::XMLElement* DBServer = root->FirstChildElement("DBServer");
   if (!DBServer) {
        printf("缺失数据库服务器配置.");
        return -1;
   }
   IP0 = DBServer->FirstChildElement("IP0");
   if (!IP0) {
           printf("数据库服务器缺失IP0.");
           return -1;
   }
   strcpy(G_V.DBIP[0],IP0->GetText());
   IP1 = DBServer->FirstChildElement("IP1");
   if (!IP1) {
           printf("数据库服务器缺失IP1.");
           return -1;
   }
   strcpy(G_V.DBIP[1],IP1->GetText());
   UDPPort = DBServer->FirstChildElement("UDPPort");
   if (!UDPPort) {
           printf("数据库服务器缺失端口配置.");
           return -1;
   }
   G_V.DBPort = atoi(UDPPort->GetText());
   return 0;
}

