
#ifndef MAP_MAPAGE_H
#define MAP_MAPAGE_H

#include <string.h>
#include <netinet/in.h>

typedef unsigned char byte;

#pragma pack(push)
#pragma pack(1)
typedef struct _MapUpateRequest     //站场示意图更新数据请求包
{
    byte          DataType;			//0x17
    uint16_t        Length;			//信息包长度
    uint32_t         MsgID;			//消息包ID  主机电脑时间秒数
    uint32_t       Version;         //版本
    uint16_t         Year;          //年
    byte            Month;          //月
    byte              Day;          //日
    byte             Hour;          //时
    byte              Min;          //分
    byte              Sec;          //秒
    byte            yuliu;          //备用
    byte              TLJ;          //局码
    uint16_t    StationID;          //站码
    byte         space[4];          //备用

    _MapUpateRequest(){
       memset(this,0,sizeof(_MapUpateRequest));
       this->DataType = 0x17;
       this->Length = sizeof(_MapUpateRequest);
    }
}MapUpateRequest;

typedef struct _MapVersionAns     //站场示意图更新-版本信息包, 版本确认包
{
    byte          DataType;			//0x88  站场示意图更新-版本信息包
                                    //0x18  站场示意图更新-版本确认包
    uint16_t        Length;			//信息包长度
    uint32_t         MsgID;			//消息包ID  电脑时间秒数
    uint32_t       Version;         //版本
    uint16_t         Year;          //年
    byte            Month;          //月
    byte              Day;          //日
    byte             Hour;          //时
    byte              Min;          //分
    byte              Sec;          //秒
    byte            yuliu;          //备用
    byte              TLJ;          //局码
    uint16_t    StationID;          //站码
    char     FileName[64];          //文件名
    uint32_t     FileSize;          //文件大小
    uint32_t    FilePackN;          //传输文件所需包数
    byte         space[4];          //备用
    char          MD5[33];          //文件MD5码
    _MapVersionAns(){
       memset(this,0,sizeof(_MapVersionAns));
       this->Length = sizeof(_MapVersionAns);
    }
}MapVersionAns;

typedef struct _MapDatBWHead     //站场示意图更新数据包包头
{
    byte          DataType;			//0x89 站场示意图更新数据包包头
    uint16_t        Length;			//信息包长度
    uint32_t         MsgID;			//消息包ID  电脑时间秒数
    uint32_t       Version;         //版本
    uint16_t         Year;          //年
    byte            Month;          //月
    byte              Day;          //日
    byte             Hour;          //时
    byte              Min;          //分
    byte              Sec;          //秒
    byte            yuliu;          //备用
    byte              TLJ;          //局码
    uint16_t    StationID;          //站码
    uint32_t     CurPackN;          //当前数据包数
    uint32_t    FilePackN;          //传输文件所需包数
    uint16_t     FileSize;          //文件大小
    _MapDatBWHead(){
       memset(this,0,sizeof(_MapDatBWHead));
       this->DataType = 0x89;
       this->Version =  56;
    }
}MapDatBWHead;


typedef struct _MapDatACK     //站场示意图更新-数据确认包
{
    byte          DataType;			//0x19 站场示意图更新-数据确认包
    uint16_t        Length;			//信息包长度
    uint32_t         MsgID;			//消息包ID  电脑时间秒数
    uint32_t       Version;         //版本
    uint16_t         Year;          //年
    byte            Month;          //月
    byte              Day;          //日
    byte             Hour;          //时
    byte              Min;          //分
    byte              Sec;          //秒
    byte            yuliu;          //备用
    byte              TLJ;          //局码
    uint16_t    StationID;          //站码
    uint32_t     CurPackN;          //当前数据包数
    uint32_t    FilePackN;          //传输文件所需包数
    _MapDatACK(){
       memset(this,0,sizeof(_MapDatACK));
       this->DataType = 0x19;
    }
}MapDatACK;
#pragma pack(pop)

#endif //MAP_MAPAGE_H