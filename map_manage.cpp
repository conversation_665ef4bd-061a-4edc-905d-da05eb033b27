//地图升级模块
#include <sys/stat.h> 
#include <fcntl.h>
#include <unistd.h>
#include <time.h>
#include <map>
#include "map_manage.h"
#include "DS_Data.h"
#include "ds_cal.h"
#include "md5.h"
#include "tx2_filelog.h"

#define READ_DATA_SIZE	1024
#define MD5_SIZE		16
#define MD5_STR_LEN		(MD5_SIZE * 2)

int frameN=0;

typedef struct _MapFileInfo
{
   char      MD5[MD5_STR_LEN + 1];     //地图文件MD5码
   int                   filesize;     //地图文件长度
   byte                     *fbuf;     //地图数据
   uint32_t                   ver;     //地图文件版本
}MapFileInfo;


struct StationPoint {
    int StationID;     //站场ID
    int TLJ;           //铁路局ID
    // 重载 '<' 运算符
    bool operator<(const StationPoint& other) const {
        if (StationID == other.StationID) {
            return TLJ < other.TLJ;
        }
        return StationID < other.StationID;
    }
};


std::mutex mtxMap; 
std::map<StationPoint, MapFileInfo> station_m;


typedef struct _MapBuf
{
  struct sockaddr_in client_addr;//请求端地址
  ForwBuf fbuf;  
}MapBuf;

mutex mtxmap;
queue<MapBuf>MapQue;

extern StaticVar G_V;

//计算文件MD5码
int Compute_file_md5(const char *file_path, char *md5_str)
{
    int i;
    int fd;
    int ret;
    unsigned char data[READ_DATA_SIZE];
    unsigned char md5_value[MD5_SIZE];
    MD5_CTX md5;

    fd = open(file_path, O_RDONLY);
    if (-1 == fd)
    {
        perror("open");
        return -1;
    }

    // init md5
    MD5Init(&md5);

    while (1)
    {
        ret = read(fd, data, READ_DATA_SIZE);
        if (-1 == ret)
        {
            perror("read");
            return -1;
        }

        MD5Update(&md5, data, ret);

        if (0 == ret || ret < READ_DATA_SIZE)
        {
            break;
        }
    }

    close(fd);

    MD5Final(&md5, md5_value);

    for(i = 0; i < MD5_SIZE; i++)
    {
        snprintf(md5_str + i*2, 2+1, "%02x", md5_value[i]);
    }
    md5_str[MD5_STR_LEN] = '\0'; // add end

    return 0;
}


//获取站场图当前版本相关信息：版本号、MD5码，大小
uint32_t GetCurMapInfo(byte TLJ, uint16_t   StationID,char *F_MD5,int *fsize)
{
   FILE *fp;
   int ver=0;
   char fname[256];
   unsigned char BigV,SmallV;
   if (TLJ==0 && StationID==0){//站场图索引文件
      sprintf(fname,"%s/station.idx",G_V.Map_Path);   
   }else{//站场图文件
      sprintf(fname,"%s/%d/%d/%d.map",G_V.Map_Path,TLJ,StationID,StationID);
   }   
   fp=fopen(fname,"rb"); 
   fread(&BigV,1,1,fp);
   fread(&SmallV,1,1,fp);  
   ver=256*BigV+SmallV;
   fclose(fp);
   Compute_file_md5(fname,F_MD5);
   struct stat fileStat;
   stat(fname, &fileStat);
   *fsize=fileStat.st_size;
   return ver;
}


//回答lsp 0x17报文
int Ans17(MapUpateRequest *bw,sockaddr_in client_addr)
{
    auto it=station_m.find(StationPoint{bw->StationID,bw->TLJ});
    if(it==station_m.end())
       return 0;
    byte bbuf[1024]={0};
    int  tlen=0;
    byte *p_cl_ad;//lsp socket 回传数据用
    p_cl_ad=(byte *)&client_addr;
    for(int i=0;i<16;i++){
        bbuf[tlen++]=p_cl_ad[i];
    }
    MapVersionAns ans;
    ans.MsgID=bw->MsgID;
    ans.DataType=0x88;
    ans.StationID=bw->StationID;
    ans.TLJ=bw->TLJ;
    ans.Version=station_m[StationPoint{bw->StationID,bw->TLJ}].ver;
    ans.yuliu=0x00;
    ans.MsgID=bw->MsgID;

    if (bw->TLJ==0 && bw->StationID==0){//站场图索引文件
       sprintf(ans.FileName,"%s/station.idx",G_V.Map_Path);   
    }else{//站场图文件
       sprintf(ans.FileName,"%s/%d/%d/%d.map",G_V.Map_Path,bw->TLJ,bw->StationID,bw->StationID);
    }
    strcpy(ans.MD5,station_m[StationPoint{bw->StationID,bw->TLJ}].MD5);
    ans.FileSize=station_m[StationPoint{bw->StationID,bw->TLJ}].filesize;
    ans.FilePackN=ans.FileSize/1024;
    if (ans.FileSize % 1024) { // 如果有余数
         ans.FilePackN++; // 增加一块
    }
    ans.Length=sizeof(ans);
    time_t         nowT;
    struct tm        *t;
    time(&nowT);
    t=localtime(&nowT);
    ans.Year  = t->tm_year+1900;
    ans.Month = t->tm_mon+1;
    ans.Day   = t->tm_mday;
    ans.Hour  = t->tm_hour;
    ans.Min   = t->tm_min;
    ans.Sec   = t->tm_sec;
    memcpy(&bbuf[16],&ans,sizeof(ans));
    tlen = tlen + sizeof(ans);
    SendBuftoCMS(bbuf,tlen,0,frameN++);
}

//回答lsp 0x18报文
int Ans18(MapVersionAns *bw,sockaddr_in client_addr)
{
    auto it=station_m.find(StationPoint{bw->StationID,bw->TLJ});
    if(it==station_m.end())
       return 0;   
    
    byte bbuf[2048]={0};
    int  tlen=0;
    byte *p_cl_ad;//lsp socket 回传数据用
    p_cl_ad=(byte *)&client_addr;
    for(int i=0;i<16;i++){
        bbuf[tlen++]=p_cl_ad[i];
    }
    MapDatBWHead ans;
    ans.MsgID=bw->MsgID;
    ans.DataType=0x89;
    ans.Version =bw->Version;
    time_t         nowT;
    struct tm        *t;
    time(&nowT);
    t=localtime(&nowT);
    ans.Year  = t->tm_year+1900;
    ans.Month = t->tm_mon+1;
    ans.Day   = t->tm_mday;
    ans.Hour  = t->tm_hour;
    ans.Min   = t->tm_min;
    ans.Sec   = t->tm_sec;
    ans.TLJ   =bw->TLJ;
    ans.StationID =bw->StationID;
    ans.CurPackN =1;
    ans.FilePackN=bw->FilePackN;
    ans.FileSize=bw->FileSize;
   
    int rsize=1024;
    if (station_m[StationPoint{bw->StationID,bw->TLJ}].filesize<1024) 
         rsize=station_m[StationPoint{bw->StationID,bw->TLJ}].filesize; 
    memcpy(&bbuf[16+sizeof(ans)],station_m[StationPoint{bw->StationID,bw->TLJ}].fbuf,rsize);
    ans.Length = rsize+sizeof(ans);
    memcpy(&bbuf[16],&ans,sizeof(ans));
    tlen = tlen + sizeof(ans)+rsize;
    SendBuftoCMS(bbuf,tlen,1,frameN++);
}


//回答lsp 0x19报文
int Ans19(MapDatACK *bw,sockaddr_in client_addr)
{
    auto it=station_m.find(StationPoint{bw->StationID,bw->TLJ});
    if(it==station_m.end())
       return 0;   
   
    byte bbuf[2048]={0};
    int  tlen=0;
    byte *p_cl_ad;//lsp socket 回传数据用
    p_cl_ad=(byte *)&client_addr;
    for(int i=0;i<16;i++){
        bbuf[tlen++]=p_cl_ad[i];
    }
    MapDatBWHead ans;
    ans.MsgID=bw->MsgID;
    ans.DataType=0x89;
    ans.Version =bw->Version;
    time_t         nowT;
    struct tm        *t;
    time(&nowT);
    t=localtime(&nowT);
    ans.Year  = t->tm_year+1900;
    ans.Month = t->tm_mon+1;
    ans.Day   = t->tm_mday;
    ans.Hour  = t->tm_hour;
    ans.Min   = t->tm_min;
    ans.Sec   = t->tm_sec;
    ans.TLJ   =bw->TLJ;
    ans.StationID =bw->StationID;
    ans.CurPackN =bw->CurPackN+1;
    ans.FilePackN=bw->FilePackN;
 /*  
    char fname[1024];
    if (bw->TLJ==0 && bw->StationID==0){//站场图索引文件
       sprintf(fname,"%s/station.idx",G_V.Map_Path);   
    }else{//站场图文件
       sprintf(fname,"%s/%d/%d/%d.map",G_V.Map_Path,bw->TLJ,bw->StationID,bw->StationID);
    }
    struct stat fileStat;
    stat(fname, &fileStat);
 */   
    ans.FileSize=station_m[StationPoint{bw->StationID,bw->TLJ}].filesize;
 /*  
    FILE* fp;
    fp=fopen(fname,"rb");
    if(fp==NULL){
       return -1;
    }
        // 移动文件指针到指定位置
    if(fseek(fp, 1024*bw->CurPackN, SEEK_SET) != 0) { // SEEK_SET 表示从文件开始位置计算偏移
        fclose(fp);
        return -1;
    }

    int rsize=fread(bbuf+16+sizeof(ans),1,1024,fp);
    fclose(fp);
*/
    
    int rsize=station_m[StationPoint{bw->StationID,bw->TLJ}].filesize-1024*bw->CurPackN;
    if(rsize>=1024) rsize=1024;
    memcpy(&bbuf[16+sizeof(ans)],&station_m[StationPoint{bw->StationID,bw->TLJ}].fbuf[1024*bw->CurPackN],rsize);


    ans.Length = rsize+sizeof(ans);
    memcpy(&bbuf[16],&ans,sizeof(ans));
    tlen = tlen + sizeof(ans)+rsize;
    if(rsize>300)
      SendBuftoCMS(bbuf,tlen,1,frameN++);
    else
      SendBuftoCMS(bbuf,tlen,0,frameN++);  
}


//站场图业务数据入队列
void PushMapBuf(struct sockaddr_in client_addr,ForwBuf fBuf)
{
    mtxmap.lock();
    MapBuf tempBuf;
    tempBuf.client_addr=client_addr;
    tempBuf.fbuf=fBuf;
    int z=MapQue.size();
    MapQue.push(tempBuf);
    z=MapQue.size();
    mtxmap.unlock();
}

//处理站场图业务数据
int ProcessMapBuf(MapBuf *tempBuf)
{
    int stat =0;
    int tempv;
    byte iType=tempBuf->fbuf.buf[26];
    switch (iType)
    {
        case 0x17://站场示意图更新-数据请求包
            Ans17((MapUpateRequest*)&tempBuf->fbuf.buf[26],tempBuf->client_addr);
            break;
        case 0x18://站场示意图更新-版本确认包
            Ans18((MapVersionAns*)&tempBuf->fbuf.buf[26],tempBuf->client_addr);
            break;
        case 0x19://站场示意图更新-数据确认包
             Ans19((MapDatACK*)&tempBuf->fbuf.buf[26],tempBuf->client_addr);
            break;    
        default:
            break;
    }   
}


//处理站场图业务员数据队列
void *Thread_ProcessMapQue(void *lpPara)
{
    while(1){
        mtxmap.lock();
        if(!MapQue.empty()){
            MapBuf tempBuf;
            tempBuf=MapQue.front();
            ProcessMapBuf(&tempBuf);
            MapQue.pop();
            mtxmap.unlock();
        }else{
            mtxmap.unlock();
        }
        usleep(10);
    }
}

byte *GetFileData(char *fname,int fsize)
{
   FILE *fp;
   fp = fopen(fname, "rb");
   byte* fbuf = (byte*)malloc(fsize);//文件字节数
   fread(fbuf, 1, fsize, fp);  
   fclose(fp);
   return fbuf;
}

//检查是否需要加载地图数据到内存
void LoadStationDat()
{
   char MD5[MD5_STR_LEN + 1];
   int fsize;
   uint32_t ver;

   //更新索引内存数据
   ver =  GetCurMapInfo(0, 0,MD5,&fsize);
   auto it=station_m.find(StationPoint{0,0});
   char fname[128];
   if (it == station_m.end() || station_m[StationPoint{0,0}].ver < ver) {
       station_m[StationPoint{0,0}].ver=ver;
       station_m[StationPoint{0,0}].filesize=fsize;
       strcpy(station_m[StationPoint{0,0}].MD5,MD5);
       free(station_m[StationPoint{0,0}].fbuf);
       sprintf(fname,"%s/station.idx",G_V.Map_Path);
       station_m[StationPoint{0,0}].fbuf=GetFileData(fname,fsize);
       if(station_m[StationPoint{0,0}].fbuf==NULL){
           rk_syslog("索引文件分配内存失败.");       
       }
    }
    //更新站场图内存数据
    byte TLJ;
    uint16_t StationID;
    for(int i=0;i<G_V.ltdw_stations.size();i++){

        TLJ=G_V.ltdw_stations[i].TLJ;
        StationID=G_V.ltdw_stations[i].StationID;
        ver =  GetCurMapInfo(TLJ, StationID,MD5,&fsize);
        auto it_s=station_m.find(StationPoint{StationID,TLJ});
        if (it_s == station_m.end() || station_m[StationPoint{StationID,TLJ}].ver < ver) {
            station_m[StationPoint{StationID,TLJ}].ver=ver;
            station_m[StationPoint{StationID,TLJ}].filesize=fsize;
            strcpy(station_m[StationPoint{StationID,TLJ}].MD5,MD5);
            free(station_m[StationPoint{StationID,TLJ}].fbuf);
            sprintf(fname,"%s/%d/%d/%d.map",G_V.Map_Path,TLJ,StationID,StationID);
            station_m[StationPoint{StationID,TLJ}].fbuf=GetFileData(fname,fsize);
            if(station_m[StationPoint{StationID,TLJ}].fbuf==NULL){
                rk_syslog("索引文件分配内存失败.");
            }
        }
    }
}

///周期性检查地图文件是否需要加载到内存
void *Thead_LoadMap(void *lpPara)
{
    while(1){
      if(G_V.IDX_V==0) continue;  
      LoadStationDat();
      sleep(3600);
    }
}